import functools
import logging
import math
import os
from typing import TYPE_CHECKING, Callable, Dict, List, Optional, Tuple, Union, cast

import tensorrt
import timm
import torch
import torch.nn.functional as F
from timm.models.resnest import ResNestBottleneck, _create_resnest
from torch import nn
from torch.utils import checkpoint
from torchvision.models._utils import IntermediateLayerGetter
from torchvision.models.densenet import densenet121
from torchvision.models.resnet import ResNet18_Weights, ResNet50_Weights, resnet18, resnet50

from deeplearning.deepweed.config import DeepweedConfig
from deeplearning.deepweed.datasets_types import DatasetLabel
from deeplearning.deepweed.point_utils import compute_downsampled_size, get_zero_tensors, make_centroid_tensors
from deeplearning.deepweed.version import get_version
from deeplearning.parametric_umap.model import ParametricUMAP
from deeplearning.trt_extensions.precision import TRTPrecision
from deeplearning.utils.aspp_layers import ASPP
from deeplearning.utils.resnet import get_resnet50_weights_url
from generated.cv.runtime.proto.cv_runtime_pb2 import HitClass
from lib.common.collections.list import flatten

POINT_STRIDES = [1]
MASK_DOWNSAMPLE = 32

# Size multiplier allows us to represent points that have radius larger than our downsampling size.
SIZE_MULTIPLIER = 100.0

# TODO: https://github.com/pytorch/pytorch/issues/31765
if TYPE_CHECKING:
    torch_jit_unused = lambda x: x
else:
    torch_jit_unused = torch.jit.unused

LOG = logging.getLogger(__name__)


def crop_and_pad_border(input: torch.Tensor, border_px: int, logits: bool = False) -> torch.Tensor:
    border_px_diff = min(input.shape[-1], input.shape[-2]) - border_px * 2
    if border_px_diff < 0:
        border_px = int(border_px + border_px_diff / 2)

    if logits:
        output = F.pad(
            input[..., border_px : input.shape[-2] - border_px, border_px : input.shape[-1] - border_px],
            (border_px, border_px, border_px, border_px),
            value=-float("inf"),
        )
    else:
        output = F.pad(
            input[..., border_px : input.shape[-2] - border_px, border_px : input.shape[-1] - border_px],
            (border_px, border_px, border_px, border_px),
        )
    return output


def get_resnet50(load_weights: bool, norm_layer=nn.BatchNorm2d):

    if load_weights:
        weights = ResNet50_Weights.DEFAULT
        weights.value.url = get_resnet50_weights_url()
    else:
        weights = None

    model = IntermediateLayerGetter(
        resnet50(weights=weights, replace_stride_with_dilation=[False, False, True], norm_layer=norm_layer),
        # We're cutting backbone short and exiting at layer3 (instead of layer4)
        # Because of this, the last replace_stride_with_dilation flag is irrelevant,
        # but we're keeping it here so that reverting back  to layer4 for experiments
        # is easier.
        return_layers={"layer3": "out"},
    )

    output_dim = 1024

    return model, output_dim


class ResNet50_FPN(nn.Module):
    def __init__(self, load_weights: bool, norm_layer=nn.BatchNorm2d):
        super().__init__()
        if load_weights:
            weights = ResNet50_Weights.DEFAULT
            weights.value.url = get_resnet50_weights_url()
        else:
            weights = None

        self.model = IntermediateLayerGetter(
            resnet50(weights=weights, norm_layer=norm_layer), return_layers={"layer3": "layer3", "layer4": "layer4"},
        )
        self.conv1 = nn.Conv2d(2048, 1024, 1)
        self.conv2 = nn.Conv2d(1024, 1024, 1)

    def forward(self, input: torch.Tensor) -> Dict[str, torch.Tensor]:
        layers = self.model(input)
        layer3 = layers["layer3"]
        layer4 = layers["layer4"]

        x = self.conv1(layer4)
        x = F.interpolate(x, size=(layer3.shape[2], layer3.shape[3]), mode="nearest")
        x += self.conv2(layer3)
        return {"out": x}


def get_resnet50_fpn(load_weights: bool, norm_layer=nn.BatchNorm2d):
    return ResNet50_FPN(load_weights, norm_layer), 1024


class ResNest50d_FPN(nn.Module):
    def __init__(self, load_weights: bool, norm_layer=nn.BatchNorm2d):
        super().__init__()
        if load_weights:
            weights = ResNet50_Weights.DEFAULT
            weights.value.url = get_resnet50_weights_url()
        else:
            weights = None

        self.model = IntermediateLayerGetter(
            timm.create_model(
                "resnest50d_4s2x40d.in1k", pretrained=load_weights, features_only=True, norm_layer=norm_layer
            ),
            # We're cutting backbone short and exiting at layer3 (instead of layer4)
            # Because of this, the last replace_stride_with_dilation flag is irrelevant,
            # but we're keeping it here so that reverting back  to layer4 for experiments
            # is easier.
            return_layers={"layer3": "layer3", "layer4": "layer4"},
        )
        self.conv1 = nn.Conv2d(2048, 1024, 1)
        self.conv2 = nn.Conv2d(1024, 1024, 1)

    def forward(self, input: torch.Tensor) -> Dict[str, torch.Tensor]:
        layers = self.model(input)
        layer3 = layers["layer3"]
        layer4 = layers["layer4"]

        x = self.conv1(layer4)
        x = F.interpolate(x, size=(layer3.shape[2], layer3.shape[3]), mode="nearest")
        x += self.conv2(layer3)
        return {"out": x}


def get_resnest50d_fpn(load_weights: bool, norm_layer=nn.BatchNorm2d):
    return ResNest50d_FPN(load_weights, norm_layer), 1024


def get_resnest50d(load_weights: bool, norm_layer=nn.BatchNorm2d):

    model = IntermediateLayerGetter(
        timm.create_model(
            "resnest50d_4s2x40d.in1k", pretrained=load_weights, features_only=True, norm_layer=norm_layer
        ),
        # We're cutting backbone short and exiting at layer3 (instead of layer4)
        # Because of this, the last replace_stride_with_dilation flag is irrelevant,
        # but we're keeping it here so that reverting back  to layer4 for experiments
        # is easier.
        return_layers={"layer3": "out"},
    )

    output_dim = 1024

    return model, output_dim


class ResNest_Custom_FPN(nn.Module):
    def __init__(self, norm_layer=nn.BatchNorm2d):
        super().__init__()

        model_kwargs = dict(
            block=ResNestBottleneck,
            layers=[3, 4, 6, 3],
            stem_type="deep",
            stem_width=32,
            avg_down=True,
            base_width=12,
            cardinality=4,
            block_args=dict(radix=1, avd=True, avd_first=True),
        )
        self.model = IntermediateLayerGetter(
            _create_resnest("", pretrained=False, **dict(model_kwargs, features_only=True, norm_layer=norm_layer)),
            return_layers={"layer3": "layer3", "layer4": "layer4"},
        )
        self.conv1 = nn.Conv2d(2048, 1024, 1)
        self.conv2 = nn.Conv2d(1024, 1024, 1)

    def forward(self, input: torch.Tensor) -> Dict[str, torch.Tensor]:
        layers = self.model(input)
        layer3 = layers["layer3"]
        layer4 = layers["layer4"]

        x = self.conv1(layer4)
        x = F.interpolate(x, size=(layer3.shape[2], layer3.shape[3]), mode="nearest")
        x += self.conv2(layer3)
        return {"out": x}


def get_resnest_custom_fpn(norm_layer=nn.BatchNorm2d):
    return ResNest_Custom_FPN(norm_layer=norm_layer), 1024


def get_resnet18(load_weights: bool, norm_layer=nn.BatchNorm2d):

    if load_weights:
        weights = ResNet18_Weights.DEFAULT
    else:
        weights = None

    model = IntermediateLayerGetter(resnet18(weights=weights, norm_layer=norm_layer), return_layers={"layer3": "out"},)

    output_dim = 256

    return model, output_dim


def get_densnet121(load_weights: bool, dropout_rate: float, norm_layer=nn.BatchNorm2d):
    model = IntermediateLayerGetter(
        densenet121(pretrained=load_weights, drop_rate=dropout_rate), return_layers={"features": "out"}
    )
    return model, 1024


class TRTEmbedding(nn.Module):
    def __init__(
        self, num_embeddings: int, embedding_dim: int, fixed_crop_idx: Optional[int] = None,
    ):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.fc = nn.Linear(num_embeddings, embedding_dim, bias=False)
        self.one_hot = None
        if fixed_crop_idx is not None:
            self.one_hot = torch.zeros((1, self.num_embeddings))
            self.one_hot[:, fixed_crop_idx] = 1

        self.fixed_crop_idx = fixed_crop_idx

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size = x.shape[0]

        if self.one_hot is not None:
            one_hot = torch.cat([self.one_hot] * batch_size)
            return cast(torch.Tensor, self.fc(one_hot.to(x.device)))

        one_hot = (
            torch.arange(self.num_embeddings, dtype=torch.int, device=x.device).unsqueeze(0).expand(batch_size, -1)
            == x.unsqueeze(-1).expand(-1, self.num_embeddings)
        ).float()
        return cast(torch.Tensor, self.fc(one_hot))


class Deepweed(nn.Module):
    SUPPORTED_HIT_CLASSES = [HitClass.WEED, HitClass.CROP, HitClass.PLANT]

    def __init__(  # noqa: C901
        self,
        num_weed_point_classes: int,
        num_segm_classes: int,
        num_crop_ids: int = 0,
        enable_crop_embeddings: bool = False,
        batch_norm=nn.BatchNorm2d,
        discard_points_border_px: Optional[float] = None,
        pretrained_model: Optional[str] = None,
        pretrained_segmentation_model: Optional[str] = None,
        frozen_backbone_point_layers: Optional[List[str]] = None,
        relu_inplace: bool = True,
        disable_crop: bool = False,
        freeze_segmentation: bool = False,
        config: DeepweedConfig = DeepweedConfig(),
        use_pumap_head: bool = False,
        fixed_crop_idx: Optional[int] = None,  # For crop embedding TRT,
        crop_id_idxes: Optional[List[int]] = None,
    ):
        super().__init__()
        self._num_weed_point_classes = num_weed_point_classes
        self._num_segm_classes = num_segm_classes
        self._num_hits = len(Deepweed.SUPPORTED_HIT_CLASSES)
        self._num_crop_ids = num_crop_ids
        self._enable_crop_embeddings = enable_crop_embeddings
        self._size_multiplier = SIZE_MULTIPLIER
        self._enable_segmentation = self._num_segm_classes > 0
        self._enable_point = self._num_weed_point_classes > 0
        self._mask_downsample = MASK_DOWNSAMPLE
        self._discard_points_border_px = discard_points_border_px or 0
        self._version = get_version()
        self._disable_crop = disable_crop
        self._relu_inplace = relu_inplace
        self._freeze_segmentation = freeze_segmentation
        assert self._enable_segmentation or self._enable_point, "At least one type of output should be enabled"
        self._frozen_backbone_point_layers = frozen_backbone_point_layers
        self._config = config
        self._fixed_crop_idx = fixed_crop_idx
        self._crop_id_idxes = crop_id_idxes

        if self._enable_crop_embeddings and self._crop_id_idxes:
            assert len(self._crop_id_idxes) == self._num_crop_ids

        if self._config.gradient_checkpoint:
            # Momentum adjustment because batch norms will be forwarded twice
            # https://github.com/prigoyal/pytorch_memonger/blob/master/tutorial/Checkpointing_for_PyTorch_models.ipynb
            norm_layer = functools.partial(batch_norm, momentum=(1 - (1 - 0.1) ** 0.5))
        else:
            norm_layer = batch_norm

        self.backbone_point = None
        if self._enable_point:
            if self._config.backbone_architecture == "resnet50":
                self.backbone_point, point_backbone_output_dim = get_resnet50(
                    load_weights=pretrained_model is None, norm_layer=norm_layer
                )
            elif self._config.backbone_architecture == "resnet50_fpn":
                self.backbone_point, point_backbone_output_dim = get_resnet50_fpn(
                    load_weights=pretrained_model is None, norm_layer=norm_layer
                )
            elif self._config.backbone_architecture == "resnest50d_fpn":
                self.backbone_point, point_backbone_output_dim = get_resnest50d_fpn(
                    load_weights=pretrained_model is None, norm_layer=norm_layer
                )
            elif self._config.backbone_architecture == "resnest50d":
                self.backbone_point, point_backbone_output_dim = get_resnest50d(
                    load_weights=pretrained_model is None, norm_layer=norm_layer
                )
            elif self._config.backbone_architecture == "resnest_custom_fpn":
                self.backbone_point, point_backbone_output_dim = get_resnest_custom_fpn(norm_layer=norm_layer)
            elif self._config.backbone_architecture == "resnet18":
                self.backbone_point, point_backbone_output_dim = get_resnet18(
                    load_weights=pretrained_model is None, norm_layer=norm_layer
                )
            elif self._config.backbone_architecture == "densenet121":
                self.backbone_point, point_backbone_output_dim = get_densnet121(
                    pretrained_model is None, self._config.backbone_dropout_rate
                )

        self.backbone_segm = None
        if self._enable_segmentation:
            self.backbone_segm, segm_output_dim = get_resnet50(
                load_weights=pretrained_model is None, norm_layer=norm_layer
            )
            self.aspp = ASPP(segm_output_dim, [12, 24, 36], batch_norm, relu_inplace=self._relu_inplace)

            # TODO(asergeev): This is the slowest layer of the model now, verify if it can be slimmed down.
            self.out_conv = nn.Sequential(
                nn.Conv2d(256, 256, 3, padding=1, bias=False),
                batch_norm(256),
                nn.ReLU(inplace=self._relu_inplace),
                nn.Dropout(self._config.dropout_rate),
                nn.Conv2d(256, self._num_segm_classes, 1),
            )

        if not self._relu_inplace:
            if self.backbone_segm is not None:
                for name, value in self.backbone_segm.named_modules():
                    if "relu" in name.lower():
                        value.inplace = False

            if self.backbone_point is not None:
                for name, value in self.backbone_point.named_modules():
                    if "relu" in name.lower():
                        value.inplace = False

        if self._enable_point:
            if self._enable_crop_embeddings:
                self.crop_emb_a = TRTEmbedding(self._num_crop_ids, point_backbone_output_dim, self._fixed_crop_idx)
                self.crop_emb_b = TRTEmbedding(self._num_crop_ids, point_backbone_output_dim, self._fixed_crop_idx)
            self.point_hit_convs, self.second_to_last_conv_layers, self.first_conv_layers = self._make_block(
                input_features=point_backbone_output_dim,
                output_features=256,
                hidden_features=self._config.hidden_features,
                num_outputs=self._num_hits,
                kernel_size=self._config.point_conv_kernel_size,
                padding=self._config.point_conv_padding,
                batch_norm=batch_norm,
            )
            self.point_category_convs, _, _ = self._make_block(
                input_features=point_backbone_output_dim,
                output_features=256,
                hidden_features=self._config.hidden_features,
                num_outputs=self._num_weed_point_classes,
                kernel_size=self._config.point_conv_kernel_size,
                padding=self._config.point_conv_padding,
                batch_norm=batch_norm,
            )
            self.point_offset_convs, _, _ = self._make_block(
                input_features=point_backbone_output_dim,
                output_features=512,
                hidden_features=self._config.hidden_features,
                num_outputs=2,
                kernel_size=self._config.point_conv_kernel_size,
                padding=self._config.point_conv_padding,
                batch_norm=batch_norm,
            )
            self.point_size_convs, _, _ = self._make_block(
                input_features=point_backbone_output_dim,
                output_features=256,
                hidden_features=self._config.hidden_features,
                num_outputs=1,
                kernel_size=self._config.point_conv_kernel_size,
                padding=self._config.point_conv_padding,
                batch_norm=batch_norm,
            )

            if self._config.use_embedding_head:
                self.embedding_conv = nn.Sequential(
                    nn.Conv2d(point_backbone_output_dim, point_backbone_output_dim, 1, bias=False),
                    batch_norm(point_backbone_output_dim),
                    nn.ReLU(inplace=self._relu_inplace),
                    nn.Conv2d(point_backbone_output_dim, point_backbone_output_dim, 1, bias=False),
                    batch_norm(point_backbone_output_dim),
                    nn.ReLU(inplace=self._relu_inplace),
                    nn.Conv2d(point_backbone_output_dim, point_backbone_output_dim, 1, bias=False),
                )

        self.trt_precision_layer = TRTPrecision()

        # Freezing layers as this head is not needed for training
        self._pumap_head = ParametricUMAP()
        for _, param in self._pumap_head.named_parameters():
            param.requires_grad_(False)
        self._use_pumap_head = use_pumap_head

        state_dict = None
        if pretrained_model is not None:
            pretrain_state_dict = torch.load(pretrained_model, map_location=torch.device("cpu"))["state_dict"]
            state_dict = {}
            for key, val in pretrain_state_dict.items():
                if key.split(".", maxsplit=1)[0] in config.pretrain_layer_prefix_skip_list:
                    continue

                if "crop_emb" in key and self._enable_crop_embeddings and self._crop_id_idxes is not None:
                    val = val[:, self._crop_id_idxes]

                state_dict[key] = val

        if (
            pretrained_segmentation_model is not None
            and os.path.exists(pretrained_segmentation_model)
            and self._enable_segmentation
        ):
            best_ckpt = torch.load(pretrained_segmentation_model, map_location=torch.device("cpu"))
            best_ckpt_state_dict = best_ckpt["state_dict"]

            segmentation_params = {}
            for key, val in best_ckpt_state_dict.items():
                if key.startswith("out_conv.") or key.startswith("aspp.") or key.startswith("backbone_segm."):
                    segmentation_params[key] = val

            if state_dict is None:
                state_dict = self.state_dict()

            state_dict.update(segmentation_params)

        if state_dict is not None:
            missing_keys, unexpected_keys = self.load_state_dict(state_dict, strict=False)
            missing_keys = [
                k for k in missing_keys if k.split(".", maxsplit=1)[0] not in config.pretrain_layer_prefix_skip_list
            ]
            if len(missing_keys) > 0:
                LOG.warn(f"Number of Missing Keys in state dict: {len(missing_keys)}")
            if len(unexpected_keys) > 0:
                LOG.warn(f"Number of Unexpected Keys in state dict: {len(unexpected_keys)}")

        self._frozen_layers = []

        if self._frozen_backbone_point_layers is not None:
            self._frozen_layers = self._freeze_backbone_point_layers()

        self._backbone_point_grad_enabled = self.backbone_point is not None and self._is_grad_enabled(
            self.backbone_point
        )

        if self._freeze_segmentation and self._enable_segmentation and self.backbone_segm is not None:
            self.out_conv.requires_grad_(False)
            self.aspp.requires_grad_(False)
            self.backbone_segm.requires_grad_(False)

        self._predict_segmentation = not self._freeze_segmentation

    def eval_frozen_layers(self) -> None:
        for layer in self._frozen_layers:
            layer.train(False)

    def set_pumap_head(self, pumap_model: ParametricUMAP) -> None:
        self._pumap_head = pumap_model
        self._use_pumap_head = True

    @property
    def use_pumap_head(self) -> bool:
        return self._use_pumap_head

    def forward(
        self,
        input: torch.Tensor,
        crop_id_idx: Optional[torch.Tensor] = None,
        gradient_checkpoint: bool = False,
        preserve_zero_tensor_shape: bool = False,
        return_pre_sigmoid_hits: bool = False,
        sigmoid: bool = True,
    ):
        assert input.shape[1] in [3, 4], f"Expecting input to be NCHW/N(C+D)HW: {input.shape}"

        input_shape = (input.shape[2], input.shape[3])
        # Interim is at H/4, W/4 resolution
        # Out is at H/16, W/16 resolution
        batch_size = input.shape[0]
        device = input.device
        dtype = input.dtype

        mask = self.segmentation_forward(
            input, gradient_checkpoint, input_shape, device, dtype, preserve_zero_tensor_shape, sigmoid=sigmoid,
        )

        if self._enable_point:
            if gradient_checkpoint and self._backbone_point_grad_enabled:
                out = self._run_backbone_point_checkpointed(input)
            else:
                out = self._run_backbone_point(input)

            if self._config.use_embedding_head:
                emb = torch.nn.functional.normalize(self.embedding_conv(out), dim=1)
                embedding_output = [emb] * len(self.point_hit_convs)
            else:
                embedding_output = [out] * len(self.point_hit_convs)

            if self._enable_crop_embeddings:
                # add crop embedding to backbone output
                if self._fixed_crop_idx is not None:
                    out = out * self.crop_emb_a(input).unsqueeze(-1).unsqueeze(-1)
                    out = out + self.crop_emb_b(input).unsqueeze(-1).unsqueeze(-1)
                else:
                    assert crop_id_idx is not None
                    out = out * self.crop_emb_a(crop_id_idx).unsqueeze(-1).unsqueeze(-1)
                    out = out + self.crop_emb_b(crop_id_idx).unsqueeze(-1).unsqueeze(-1)

            pre_sigmoid_point_hits = [conv(out) for conv in self.point_hit_convs]
            point_categories = [conv(out) for conv in self.point_category_convs]
            point_offsets = [self.split_channel(conv(out), factor=2) for conv in self.point_offset_convs]
            repeated_point_offsets = []
            for point_offset in point_offsets:
                repeated_point_offsets.append(torch.cat([point_offset] * self._num_hits, 1))

            if self._use_pumap_head:
                pumap_output = [self._pumap_head(embedding_output[0])] * len(self.point_hit_convs)
            else:
                pumap_output = [
                    self._make_zero_point_hit_tensor(
                        batch_size=batch_size,
                        input_shape=input_shape,
                        downsample=ds,
                        device=device,
                        dtype=dtype,
                        preserve_zero_tensor_shape=True,
                        sigmoid=True,
                        num_outputs=2,
                    )
                    for ds in self._config.point_downsample
                ]

            #############################################################
            # Set TensorRT precision to float32 for only point_size_convs
            #############################################################
            out = self.trt_precision_layer(out, tensorrt.DataType.FLOAT)
            point_sizes = [conv(out) for conv in self.point_size_convs]
            out = self.trt_precision_layer(out, None)
            #############################################################
            # TensorRT precision reset to None
            #############################################################

            repeated_point_sizes = []
            for point_size in point_sizes:
                repeated_point_sizes.append(torch.cat([point_size] * self._num_hits, 1))

            # activation function, multipliy size by SIZE_MULTIPLIER
            point_hits = [torch.sigmoid(c) if sigmoid else c for c in pre_sigmoid_point_hits]
            point_categories = [torch.sigmoid(c) if sigmoid else c for c in point_categories]
            point_offsets = [torch.tanh(c) for c in repeated_point_offsets]

            # we map neutral output of size sigmoid(0.5) to ~3 which translates to 6mm weed radius
            # so, sigmoid(c) = 0 maps to 0, sigmoid(c) = 0.5 maps to ~3, sigmoid(c) = 1.0 maps to 100
            point_sizes = [torch.sigmoid(c) * self._size_multiplier for c in repeated_point_sizes]

            # discard border
            point_hits = [
                crop_and_pad_border(x, int(self._discard_points_border_px // ds), logits=not sigmoid)
                for x, ds in zip(point_hits, self._config.point_downsample)
            ]

            if self._disable_crop:
                for x in point_hits:
                    x[:, HitClass.CROP : HitClass.CROP + 1] = 0 if sigmoid else -float("inf")

        else:
            point_hits = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=device,
                    dtype=dtype,
                    preserve_zero_tensor_shape=preserve_zero_tensor_shape,
                    num_outputs=self._num_hits,
                    sigmoid=sigmoid,
                )
                for ds in self._config.point_downsample
            ]
            point_categories = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=device,
                    dtype=dtype,
                    preserve_zero_tensor_shape=preserve_zero_tensor_shape,
                    num_outputs=self._num_weed_point_classes,
                    sigmoid=sigmoid,
                )
                for ds in self._config.point_downsample
            ]
            point_offsets = [
                self._make_zero_point_offset_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=device,
                    dtype=dtype,
                    preserve_zero_tensor_shape=preserve_zero_tensor_shape,
                )
                for ds in self._config.point_downsample
            ]
            point_sizes = [
                self._make_zero_point_size_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=device,
                    dtype=dtype,
                    preserve_zero_tensor_shape=preserve_zero_tensor_shape,
                    num_outputs=self._num_hits,
                )
                for ds in self._config.point_downsample
            ]

            pumap_output = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=device,
                    dtype=dtype,
                    preserve_zero_tensor_shape=preserve_zero_tensor_shape,
                    sigmoid=True,
                    num_outputs=2,
                )
                for ds in self._config.point_downsample
            ]

            embedding_output = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=device,
                    dtype=dtype,
                    preserve_zero_tensor_shape=preserve_zero_tensor_shape,
                    sigmoid=True,
                    num_outputs=1024,
                )
                for ds in self._config.point_downsample
            ]

        # Mask out un-interesting outputs of transposed convolutions
        result = DeepweedOutput(
            self._version,
            mask,
            point_hits,
            point_categories,
            point_offsets,
            point_sizes,
            not sigmoid,
            pumap_output,
            embedding_output,
        )

        if return_pre_sigmoid_hits:
            return result.pack(), pre_sigmoid_point_hits
        else:
            return result.pack()

    def _freeze_backbone_point_layers(self) -> List[nn.Module]:
        frozen_layers: List[nn.Module] = []
        if self._frozen_backbone_point_layers is None or self.backbone_point is None:
            return frozen_layers

        for layer in self._frozen_backbone_point_layers:
            lp = self.backbone_point[layer] if layer in self.backbone_point else None
            if lp is not None:
                frozen_layers.append(lp)
                for _, param in lp.named_parameters():
                    param.requires_grad_(False)

        return frozen_layers

    def _is_grad_enabled(self, module: nn.Module) -> bool:
        grad_required = False
        for _, param in module.named_parameters():
            if param.requires_grad:
                grad_required = True

        return grad_required

    def _make_block(
        self,
        input_features: int,
        output_features: int,
        hidden_features: int,
        num_outputs: int,
        kernel_size: int,
        padding: int,
        batch_norm=nn.BatchNorm2d,
        bias=False,
    ):
        second_to_last_convs = []
        first_convs = []
        layers = []
        for stride in POINT_STRIDES:
            first_conv = nn.Conv2d(input_features, hidden_features, kernel_size=kernel_size, padding=padding, bias=bias)
            first_convs.append(first_conv)
            second_to_last_conv = nn.Conv2d(
                hidden_features, output_features, kernel_size=kernel_size, padding=padding, bias=bias
            )
            second_to_last_convs.append(second_to_last_conv)
            layers.append(
                nn.Sequential(
                    # Layer 1
                    first_conv,
                    batch_norm(hidden_features),
                    nn.ReLU(inplace=self._relu_inplace),
                    # Flattened layers do not apply in default case. See value of POINT_STRIDES
                    *flatten(
                        [
                            [
                                nn.Conv2d(
                                    hidden_features, hidden_features, kernel_size=3, padding=1, stride=2, bias=bias
                                ),
                                batch_norm(hidden_features),
                                nn.ReLU(inplace=self._relu_inplace),
                            ]
                            for x in range(int(math.log2(stride)))
                        ]
                    ),
                    # Layer 2
                    nn.Conv2d(hidden_features, hidden_features, kernel_size=kernel_size, padding=padding, bias=bias),
                    batch_norm(hidden_features),
                    nn.ReLU(inplace=self._relu_inplace),
                    # Layer 3
                    nn.Conv2d(hidden_features, hidden_features, kernel_size=kernel_size, padding=padding, bias=bias),
                    batch_norm(hidden_features),
                    nn.ReLU(inplace=self._relu_inplace),
                    # Layer 4
                    nn.Conv2d(hidden_features, hidden_features, kernel_size=kernel_size, padding=padding, bias=bias),
                    batch_norm(hidden_features),
                    nn.ReLU(inplace=self._relu_inplace),
                    # Layer 5
                    second_to_last_conv,
                    batch_norm(output_features),
                    nn.ReLU(inplace=self._relu_inplace),
                    nn.Dropout(self._config.dropout_rate),
                    # Last layer
                    nn.Conv2d(output_features, num_outputs, kernel_size=1, bias=True),
                )
            )

        return nn.ModuleList(layers), second_to_last_convs, first_convs

    def split_channel(self, x: torch.Tensor, factor: int) -> torch.Tensor:
        """Splits channel dimension and makes factor last dimension."""
        assert x.shape[1] % factor == 0, f"Input is not splittable by {factor}: {x.shape}"
        return x.reshape(x.shape[0], int(x.shape[1] / factor), factor, x.shape[2], x.shape[3]).permute(0, 1, 3, 4, 2)

    @torch_jit_unused
    def _run_backbone_point_checkpointed(self, input):
        def run_backbone_point(input, dummy_reqs_grad):
            return self.backbone_point(input)["out"]

        # Dummy requiring gradient is necessary for backbone gradients to be computed
        dummy_reqs_grad = torch.tensor(1.0, requires_grad=True)
        return checkpoint.checkpoint(run_backbone_point, input, dummy_reqs_grad, use_reentrant=False)

    def _run_backbone_point(self, input):
        return self.backbone_point(input)["out"]

    @torch_jit_unused
    def _run_backbone_segm_checkpointed(self, input):
        def run_backbone_segm(input, dummy_reqs_grad):
            return self.backbone_segm(input)["out"]

        # Dummy requiring gradient is necessary for backbone gradients to be computed
        dummy_reqs_grad = torch.tensor(1.0, requires_grad=not self._freeze_segmentation)
        return checkpoint.checkpoint(run_backbone_segm, input, dummy_reqs_grad, use_reentrant=False)

    def _run_backbone_segm(self, input):
        return self.backbone_segm(input)["out"]

    def _make_zero_mask_tensor(
        self,
        batch_size: int,
        input_shape: Tuple[int, int],
        device: torch.device,
        dtype: torch.dtype,
        preserve_zero_tensor_shape: bool,
        sigmoid: bool,
    ):
        zero_func: Callable[..., torch.Tensor]
        if sigmoid:
            zero_func = torch.zeros
        else:
            zero_func = functools.partial(torch.full, fill_value=-float("inf"))
        if preserve_zero_tensor_shape:
            return zero_func(
                (batch_size, self._num_segm_classes)
                + (
                    math.ceil(input_shape[0] / self._mask_downsample),
                    math.ceil(input_shape[1] / self._mask_downsample),
                ),
                device=device,
                dtype=dtype,
            )
        else:
            return zero_func((batch_size,), device=device, dtype=dtype)

    def _make_zero_point_hit_tensor(
        self,
        batch_size: int,
        input_shape: Tuple[int, int],
        downsample: int,
        device: torch.device,
        dtype: torch.dtype,
        preserve_zero_tensor_shape: bool,
        num_outputs: int,
        sigmoid: bool,
    ):
        zero_func: Callable[..., torch.Tensor]
        if sigmoid:
            zero_func = torch.zeros
        else:
            zero_func = functools.partial(torch.full, fill_value=-float("inf"))
        if preserve_zero_tensor_shape:
            return zero_func(
                (batch_size, num_outputs) + compute_downsampled_size(input_shape, downsample),
                device=device,
                dtype=dtype,
            )
        else:
            return zero_func((batch_size,), device=device, dtype=dtype)

    def _make_zero_point_offset_tensor(
        self,
        batch_size: int,
        input_shape: Tuple[int, int],
        downsample: int,
        device: torch.device,
        dtype: torch.dtype,
        preserve_zero_tensor_shape: bool,
    ):
        if preserve_zero_tensor_shape:
            return torch.zeros(
                (batch_size, self._num_hits) + compute_downsampled_size(input_shape, downsample) + (2,),
                device=device,
                dtype=dtype,
            )
        else:
            return torch.zeros((batch_size,), device=device, dtype=dtype)

    def _make_zero_point_size_tensor(
        self,
        batch_size: int,
        input_shape: Tuple[int, int],
        downsample: int,
        device: torch.device,
        dtype: torch.dtype,
        preserve_zero_tensor_shape: bool,
        num_outputs: int,
    ):
        if preserve_zero_tensor_shape:
            return torch.zeros(
                (batch_size, num_outputs) + compute_downsampled_size(input_shape, downsample),
                device=device,
                dtype=dtype,
            )
        else:
            return torch.zeros((batch_size,), device=device, dtype=dtype)

    @property
    def discard_points_border_px(self) -> float:
        return self._discard_points_border_px

    def set_predict_segmentation(self, value: bool) -> None:
        self._predict_segmentation = value

    def segmentation_forward(
        self, input, gradient_checkpoint, input_shape, device, dtype, preserve_zero_tensor_shape, sigmoid: bool = True
    ):
        if self._enable_segmentation and self._predict_segmentation:
            original_dtype = input.dtype
            if original_dtype == torch.bfloat16:
                input = input.to(torch.float32)
            input_ds = F.interpolate(input, size=(input.shape[-2] // 2, input.shape[-1] // 2))
            if original_dtype == torch.bfloat16:
                input_ds = input_ds.to(torch.bfloat16)
            if gradient_checkpoint:
                out = self._run_backbone_segm_checkpointed(input_ds)
            else:
                out = self._run_backbone_segm(input_ds)
            out_aspp = self.aspp(out)
            mask = self.out_conv(out_aspp)
            if sigmoid:
                mask = torch.sigmoid(mask)
        else:
            mask = self._make_zero_mask_tensor(
                batch_size=input.shape[0],
                input_shape=input_shape,
                device=device,
                dtype=dtype,
                preserve_zero_tensor_shape=preserve_zero_tensor_shape,
                sigmoid=sigmoid,
            )

        return mask

    def fix_zero_tensor_shape(self, out: "DeepweedOutput", shape: torch.Size) -> "DeepweedOutput":
        input_shape = (shape[2], shape[3])
        batch_size = shape[0]

        if len(out.mask.shape) <= 1:
            fixed_mask = self._make_zero_mask_tensor(
                batch_size=batch_size,
                input_shape=input_shape,
                device=out.device,
                dtype=out.dtype,
                preserve_zero_tensor_shape=True,
                sigmoid=not out.pre_sigmoid,
            )
        else:
            fixed_mask = out.mask

        if len(out.point_hits[0].shape) <= 1:
            fixed_point_hits = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=out.device,
                    dtype=out.dtype,
                    preserve_zero_tensor_shape=True,
                    num_outputs=self._num_hits,
                    sigmoid=not out.pre_sigmoid,
                )
                for ds in self._config.point_downsample
            ]
        else:
            fixed_point_hits = out.point_hits

        if len(out.point_categories[0].shape) <= 1:
            fixed_point_categories = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=out.device,
                    dtype=out.dtype,
                    preserve_zero_tensor_shape=True,
                    num_outputs=self._num_weed_point_classes,
                    sigmoid=not out.pre_sigmoid,
                )
                for ds in self._config.point_downsample
            ]
        else:
            fixed_point_categories = out.point_categories

        if len(out.point_offsets[0].shape) <= 1:
            fixed_point_offsets = [
                self._make_zero_point_offset_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=out.device,
                    dtype=out.dtype,
                    preserve_zero_tensor_shape=True,
                )
                for ds in self._config.point_downsample
            ]
        else:
            fixed_point_offsets = out.point_offsets

        if len(out.point_sizes[0].shape) <= 1:
            fixed_point_sizes = [
                self._make_zero_point_size_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=ds,
                    device=out.device,
                    dtype=out.dtype,
                    preserve_zero_tensor_shape=True,
                    num_outputs=self._num_hits,
                )
                for ds in self._config.point_downsample
            ]
        else:
            fixed_point_sizes = out.point_sizes

        if len(out.pumap_output) == 0 or len(out.pumap_output[0].shape) <= 1:
            fixed_pumap_output = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=self._config.point_downsample[0],
                    device=out.device,
                    dtype=out.dtype,
                    preserve_zero_tensor_shape=True,
                    sigmoid=False,
                    num_outputs=2,
                )
            ]
        else:
            fixed_pumap_output = out.pumap_output

        if len(out.embedding_output) == 0 or len(out.embedding_output[0].shape) <= 1:
            fixed_embedding_output = [
                self._make_zero_point_hit_tensor(
                    batch_size=batch_size,
                    input_shape=input_shape,
                    downsample=self._config.point_downsample[0],
                    device=out.device,
                    dtype=out.dtype,
                    preserve_zero_tensor_shape=True,
                    sigmoid=False,
                    num_outputs=1024,
                )
            ]
        else:
            fixed_embedding_output = out.embedding_output

        return DeepweedOutput(
            version=out.version,
            mask=fixed_mask,
            point_hits=fixed_point_hits,
            point_categories=fixed_point_categories,
            point_offsets=fixed_point_offsets,
            point_sizes=fixed_point_sizes,
            pre_sigmoid=out.pre_sigmoid,
            pumap_output=fixed_pumap_output,
            embedding_output=fixed_embedding_output,
        )


class DeepweedOutput:
    def __init__(
        self,
        version: int,
        mask: torch.Tensor,
        point_hits: List[torch.Tensor],
        point_categories: List[torch.Tensor],
        point_offsets: List[torch.Tensor],
        point_sizes: List[torch.Tensor],
        pre_sigmoid: bool,
        pumap_output: List[torch.Tensor],
        embedding_output: List[torch.Tensor],
    ):
        self.mask = mask
        self.point_hits = point_hits
        self.point_categories = point_categories
        self.point_offsets = point_offsets
        self.point_sizes = point_sizes
        self.version = version
        self.pre_sigmoid = pre_sigmoid
        self.pumap_output = pumap_output
        self.embedding_output = embedding_output

    def sigmoid(self):
        if self.pre_sigmoid:
            return DeepweedOutput(
                version=self.version,
                point_hits=[torch.sigmoid(x) for x in self.point_hits],
                point_categories=[torch.sigmoid(x) for x in self.point_categories],
                point_offsets=self.point_offsets,
                point_sizes=self.point_sizes,
                mask=torch.sigmoid(self.mask),
                pre_sigmoid=False,
                pumap_output=self.pumap_output,
                embedding_output=self.embedding_output,
            )
        else:
            return self

    def fence(
        self, batch_enabled_segm_classes: torch.Tensor,
    ):
        bec_4d = batch_enabled_segm_classes.unsqueeze(-1).unsqueeze(-1)
        return DeepweedOutput(
            version=self.version,
            mask=self.mask * bec_4d,
            point_hits=self.point_hits,
            point_categories=self.point_categories,
            point_offsets=self.point_offsets,
            point_sizes=self.point_sizes,
            pre_sigmoid=self.pre_sigmoid,
            pumap_output=self.pumap_output,
            embedding_output=self.embedding_output,
        )

    def cpu(self):
        return DeepweedOutput(
            version=self.version,
            mask=self.mask.cpu(),
            point_hits=[h.cpu() for h in self.point_hits],
            point_categories=[c.cpu() for c in self.point_categories],
            point_offsets=[o.cpu() for o in self.point_offsets],
            point_sizes=[s.cpu() for s in self.point_sizes],
            pre_sigmoid=self.pre_sigmoid,
            pumap_output=[p.cpu() for p in self.pumap_output],
            embedding_output=[e.cpu() for e in self.embedding_output],
        )

    def cuda(self):
        return DeepweedOutput(
            version=self.version,
            mask=self.mask.cuda(),
            point_hits=[h.cuda() for h in self.point_hits],
            point_categories=[c.cuda() for c in self.point_categories],
            point_offsets=[o.cuda() for o in self.point_offsets],
            point_sizes=[s.cuda() for s in self.point_sizes],
            pre_sigmoid=self.pre_sigmoid,
            pumap_output=[p.cuda() for p in self.pumap_output],
            embedding_output=[e.cuda() for e in self.embedding_output],
        )

    def float(self):
        return DeepweedOutput(
            version=self.version,
            mask=self.mask.float(),
            point_hits=[h.float() for h in self.point_hits],
            point_categories=[c.float() for c in self.point_categories],
            point_offsets=[o.float() for o in self.point_offsets],
            point_sizes=[s.float() for s in self.point_sizes],
            pre_sigmoid=self.pre_sigmoid,
            pumap_output=[p.float() for p in self.pumap_output],
            embedding_output=[e.float() for e in self.embedding_output],
        )

    def detach(self):
        return DeepweedOutput(
            version=self.version,
            mask=self.mask.detach(),
            point_hits=[h.detach() for h in self.point_hits],
            point_categories=[c.detach() for c in self.point_categories],
            point_offsets=[o.detach() for o in self.point_offsets],
            point_sizes=[s.detach() for s in self.point_sizes],
            pre_sigmoid=self.pre_sigmoid,
            pumap_output=[p.detach() for p in self.pumap_output],
            embedding_output=[e.detach() for e in self.embedding_output],
        )

    @property
    def device(self):
        return self.mask.device

    @property
    def dtype(self):
        return self.mask.dtype

    @property
    def batch_size(self):
        # We ensure that dummy mask has correct batch size
        return self.mask.shape[0]

    def pack(self):
        # TorchScript JIT is dumb and cannot infer list sizes here
        return (
            torch.tensor([self.version], dtype=torch.int32, device=self.mask.device),
            self.mask,
            torch.tensor([1], dtype=torch.int32, device=self.mask.device),  # number of downsampling levels
            self.point_hits[0],
            self.point_categories[0],
            self.point_offsets[0],
            self.point_sizes[0],
            torch.tensor([self.pre_sigmoid], dtype=torch.int32, device=self.mask.device),
            self.pumap_output[0],
            self.embedding_output[0],
        )


class DeepweedOutputFactory:
    """This is separated from DeepweedOutput to make the latter TorchScript compatible."""

    @staticmethod
    def _unpack_tuple(input: Tuple[torch.Tensor, ...]) -> DeepweedOutput:
        assert len(input) >= 1, f"Invalid input size: {len(input)}"

        ptr = 0

        version = int(input[ptr][0])
        ptr += 1

        mask = input[ptr]
        ptr += 1

        strides_len = int(input[ptr][0])
        ptr += 1

        point_hits = list(input[ptr : ptr + strides_len])
        ptr += strides_len
        point_categories = list(input[ptr : ptr + strides_len])
        ptr += strides_len
        point_offsets = list(input[ptr : ptr + strides_len])
        ptr += strides_len
        point_sizes = list(input[ptr : ptr + strides_len])
        ptr += strides_len

        pre_sigmoid = False
        if len(input) > ptr:
            pre_sigmoid = bool(input[ptr][0])
            ptr += 1

        pumap_output = []
        if len(input) > ptr:
            pumap_output = list(input[ptr : ptr + strides_len])
            ptr += strides_len

        embedding_output = []
        if len(input) > ptr:
            embedding_output = list(input[ptr : ptr + 1])
            ptr += strides_len

        # We ignore additional inputs for forward-compatibility
        return DeepweedOutput(
            version,
            mask,
            point_hits,
            point_categories,
            point_offsets,
            point_sizes,
            pre_sigmoid,
            pumap_output,
            embedding_output,
        )

    @staticmethod
    def unpack(input: Union[torch.Tensor, Tuple[torch.Tensor, ...]]) -> DeepweedOutput:
        if isinstance(input, torch.Tensor):
            return DeepweedOutputFactory._unpack_tuple((input,))
        return DeepweedOutputFactory._unpack_tuple(input)

    @staticmethod
    def from_label(
        label: DatasetLabel,
        weed_category_to_index: List[str],
        discard_points_border_px: float = 0,
        use_confidence: bool = False,
        confidence_padding: int = 0,
        use_crop_protection: bool = False,
        crop_protection_padding: int = 0,
        baby_crop_size: float = 0,
        crop_protection_multiplier: float = 1,
        baby_crop_protection_multiplier: float = 1,
        point_downsample: Optional[List[int]] = None,
    ) -> Tuple[DeepweedOutput, List[torch.Tensor], List[torch.Tensor]]:
        point_downsample = point_downsample if point_downsample is not None else [16]
        if len(weed_category_to_index) > 0:
            point_hits, point_categories, point_offsets, point_sizes, point_confidence, point_crop_protection = zip(
                *[
                    make_centroid_tensors(
                        label,
                        weed_category_to_index,
                        Deepweed.SUPPORTED_HIT_CLASSES,
                        downsample=ds,
                        skip_too_small_centroids=(ds != point_downsample[0]),
                        skip_too_large_centroids=(ds != point_downsample[-1]),
                        confidence_padding=confidence_padding,
                        crop_protection_padding=crop_protection_padding,
                        baby_crop_size=baby_crop_size,
                        crop_protection_multiplier=crop_protection_multiplier,
                        baby_crop_protection_multiplier=baby_crop_protection_multiplier,
                    )
                    for ds in point_downsample
                ]
            )
        else:
            point_hits, point_categories, point_offsets, point_sizes, point_confidence, point_crop_protection = zip(
                *[
                    get_zero_tensors(
                        label,
                        len(Deepweed.SUPPORTED_HIT_CLASSES),
                        0,
                        compute_downsampled_size((label.mask.shape[2], label.mask.shape[3]), ds),
                    )
                    for ds in point_downsample
                ]
            )

        # discard border
        point_hits_list = [
            crop_and_pad_border(x, int(discard_points_border_px // ds)) for x, ds in zip(point_hits, point_downsample)
        ]

        p_conf = list(point_confidence)
        p_crop_protection = list(point_crop_protection)
        # set confidence to 1 for everything if we aren't using label confidence
        if not use_confidence:
            p_conf = [torch.ones_like(p) for p in list(point_confidence)]
        if not use_crop_protection:
            p_crop_protection = [torch.ones_like(p) for p in list(point_crop_protection)]

        # TODO round up the mask shape here and all zero mask tensor
        mask_ds = F.interpolate(
            label.mask,
            size=(
                math.ceil(label.mask.shape[-2] / MASK_DOWNSAMPLE),
                math.ceil(label.mask.shape[-1] / MASK_DOWNSAMPLE),
            ),
            mode="bilinear",
            align_corners=False,
        )
        return (
            DeepweedOutput(
                get_version(),
                mask_ds,
                point_hits_list,
                list(point_categories),
                list(point_offsets),
                list(point_sizes),
                False,
                [],
                [],
            ),
            p_conf,
            p_crop_protection,
        )


class DeepweedSeparableLayers(nn.Module):
    """
    Model wrapper for use cases where only one layer of output is expected, rather than mask,
    point hits, offsets, and sizes together. Specific filters can be passed in as well to
    sum output across that filter before returning. This is helpful for guided grad cam. [RAVEN]
    """

    def __init__(self, model: Deepweed):
        super().__init__()
        self._model = model
        self._output = None
        self._output_sum = 0.0
        self._filt = torch.zeros(0)

    def forward(self, input, gradient_checkpoint=False):
        """
        Makes a forward pass on model, gets the output layer of choice, then applies element-wise
        multiplication with the filter before summing.

        gradient_checkpoint doesn't work with captum because captum uses autograd.grad rather than .backprop,
        and gradient_checkpointing incompatable with .grad.
        """
        _, pre_sigmoid_hits = self._model(input, gradient_checkpoint, return_pre_sigmoid_hits=True)
        layer = pre_sigmoid_hits[0]
        sum = (self._filt.cuda() * layer).sum()
        self._output_sum = sum.item()

        return sum.unsqueeze(0).unsqueeze(0).unsqueeze(0)  # Captum expects 4D outputs (NxCxHxW)

    @property
    def model(self) -> Deepweed:
        return self._model

    @property
    def filter(self) -> torch.Tensor:
        return self._filt

    @filter.setter
    def filter(self, filter):
        self._filt = filter

    @property
    def output(self) -> Union[None, DeepweedOutput]:
        return self._output

    @property
    def output_sum(self) -> float:
        return self._output_sum
