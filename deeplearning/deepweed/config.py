import functools
from dataclasses import dataclass, field
from typing import List, Optional, Tuple

from dataclass_wizard import JSONWizard

from deeplearning.deepweed.constants import (
    DEFAULT_DATASET_VERSION,
    EMBEDDING_LOSS_SIZE_DIFFERENTIATION_THRESHOLD_DEFAULT,
)
from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import WANDB_PROJECT_MAP


@dataclass(eq=True, frozen=True)
class DeepweedConfig(JSONWizard):

    size_balancing: bool = False
    size_balancing_partitions: Tuple[float, float] = (8.0, 23.0)

    sampling_algorithm: str = "default"
    # Other sampling algorithm options are geohash-date-category-buckets and geohash-month
    sampling_geohash_precision: int = 4
    train_all_points_every_step: bool = False

    # parent model parameters
    pretrained_model: Optional[str] = None
    model_id: Optional[str] = None
    pretrain_layer_prefix_skip_list: List[str] = field(default_factory=list)

    # Optimizer hyperparameters
    weight_decay: float = 0.0
    momentum: float = 0.9
    nesterov: bool = False
    lr: float = 0.004
    lr_gamma: float = 0.5
    lr_milestones: List[int] = field(default_factory=functools.partial(list, [10, 20, 30, 35]))
    focal_loss: bool = False
    training_batch_size: int = 3
    evaluation_batch_size: int = 3

    # Loss hyperparameters
    point_weed_hit_loss_weight: float = 1.0
    point_crop_hit_loss_weight: float = 0.625
    point_plant_hit_loss_weight: float = 1.0
    point_category_loss_weight: float = 10.0
    point_offset_loss_weight: float = 25.0
    point_size_loss_weight: float = 3.125
    point_weight_constant: float = 4e5
    embedding_loss_weight: float = 3

    # Loss Functions
    embedding_loss_function: str = "pairwise"  # ["pairwise", "info_nce", "triplet"]

    # Other configuration settings
    point_hit_threshold: float = 0.5
    core_data_level: str = "unfiltered"
    wandb_project: str = WANDB_PROJECT_MAP[ModelUseCase.PREDICT]
    dropout_rate: float = 0.5
    backbone_dropout_rate: float = 0.2

    gradient_checkpoint: bool = True
    backbone_architecture: str = "resnet50_fpn"  # ["resnet50", "resnet50_fpn"]
    hidden_features: int = 256

    point_downsample: List[int] = field(default_factory=functools.partial(list, [16]))

    use_all_weeds: bool = False

    precision: str = "32-true"
    num_workers: int = 2
    crop_background_multiplier: float = 1.0
    segm_loss_weight: float = 1.0
    num_samples: int = 6000
    training_set_ratio: Optional[float] = None  # If defined, overrides num_samples to be a ratio of the training set
    num_epochs: int = 40
    fast_run: bool = False
    ci_run: bool = False
    save_recording_loss: bool = True

    # Fine Tuning Parameters
    goal_percentage_new: float = 0.3

    use_classical: bool = False  # Use true new data for the emphasis set
    use_recency: bool = True  # Use images that are recent for the emphasis set
    use_geohash: bool = False  # Use geohash for emphasis set
    use_robot_ids: bool = False  # Use specific robot ids for emphasis set

    emphasized_geohashes: List[str] = field(default_factory=list)  # Emphasized Geohashes if use_geohash is true
    emphasized_robot_ids: List[str] = field(
        default_factory=list
    )  # Emphasized Robot Ids if use_specific_robot_ids is true

    new_data_weight: Optional[
        float
    ] = None  # If defined, val_oec = new_data_weight * val_new_split_oec + (1 - new_data_weight) * val_old_split_oec

    min_recent_images: int = 100  # Min number of images in the new split if using recency
    recency_candidate_h: int = 72  # Anything younger than this parameter is considered recent

    dataset_balance_robot_ids: bool = False  # If true, balance dataset split generation on robot id
    dataset_balance_geohash: Optional[int] = None  # If set, balance on geoN in dataset split generation

    checkpoint_start_ratio: float = 0.5  # Ratio of epochs after which we can start checkpointing, and thus looking for model with highest val_oec

    make_trt_model: bool = True
    convert_int8: bool = True
    convert_fp16: bool = False

    comparison_model_id: Optional[str] = None
    max_comparison_pairs_for_embedding_training: int = 128

    # These define the resolution in which we divide the embedding buckets if we're balancing on embeddings
    embedding_x_buckets: List[int] = field(default_factory=functools.partial(list, [10, 10]))
    embedding_y_buckets: List[int] = field(default_factory=functools.partial(list, [10, 10]))

    # Crop protection multipliers
    use_crop_protection: bool = True
    crop_protection_padding: int = 1
    crop_protection_multiplier: float = 10
    baby_crop_protection_multiplier: float = 20
    baby_crop_size: float = 5

    # Point heads convolution settings
    point_conv_kernel_size: int = 1
    point_conv_padding: int = 0

    # Training and evaluation image size options
    training_image_height: int = 1696
    training_image_width: int = 1800

    evaluation_image_height: int = 1696
    evaluation_image_width: int = 1800

    # Embedding balancing configs
    embedding_balancing_model: Optional[str] = None  # Model that is used for get embeddings for balancing
    evaluate_embeddings_for_new_points: bool = False  # Whether to get embeddings for the training set via the embedding_balancing_model
    clustering_algorithm: str = "k_medoids_comparison"  # If balancing via embedding clustering, this is the clustering algorithm used
    clustering_min_k: int = 20  # The lowest k to consider in the cluster sweep
    clustering_max_k: int = 20  # The highest k to consider in the cluster sweep
    clustering_n: int = 20000  # The number of embeddings to cluster

    # Image augmentation
    enable_random_rotation: bool = True

    # Driptape training
    train_no_driptape_keep_multiplier: Optional[int] = 20
    val_no_driptape_keep_multiplier: Optional[int] = 3
    test_no_driptape_keep_multiplier: Optional[int] = 3

    # Driptape training for dataset V2
    train_driptape_percentage: Optional[float] = 0.05
    val_driptape_percentage: Optional[float] = 0.25
    test_driptape_percentage: Optional[float] = 0.25

    # Embedding training configs
    train_embeddings: bool = True  # Trains embeddings
    train_embeddings_pumap: bool = True  # Trains pumap embeddings
    evaluate_new_comparison_data: bool = True  # Gets comparison embeddings for new data
    embedding_positive_threshold: float = 0.8  # Any comparison similarity above this threshold will be considered a positive match
    embedding_negative_threshold: float = 0.2  # Any comparison similarity below this threshold will be considered a negative match
    embedding_margin: float = 128

    triplet_loss_random_neighbor: bool = True  # Whether to do triplet loss with a random positive and negative neighbor. If False, it will do it with all combinations of positive/negative neighbors
    save_full_embeddings_from_trt_model: bool = False  # During evaluation, saves FULL embeddings for the trt model if this is True
    save_embeddings_to_points_db: bool = True  # Whether to save embeddings to points_db file
    evaluate_full_embeddings: bool = False  # True if the output of an evaluation should be full embeddings or not
    save_embeddings_as_fp16: bool = False  # If true, stores embeddings as fp16 rather than fp32 in hdf5 file
    apply_embedding_loss_across_batch: bool = True
    average_embeddings_across_hits: bool = False  # If false, we'll pick a random embedding from the hit squares to apply loss to. If true, we'll average embeddings across those hit squares
    use_comparison_similarity_loss: bool = True
    base_embedding_label_on_hit: bool = False  # Whether the embedding label should be based on hit class in addition to comparison output: same = (comparison == same AND hit == same), different = (comparison != same OR hit != same)
    use_points_with_overlap: bool = False  # Whether the batch for embedding loss should use overlapped item sampling
    without_overlap_and_without_overlap_percentage: float = 0.5  # If use_points_with_overlap is activated, what percentage of points should be considered (without_overlap, without_overlap)
    with_overlap_and_with_overlap_percentage: float = 0.5  # If use_points_with_overlap is activated, what percentage of points should be considered (with_overlap, with_overlap)
    two_points_overlap_with_each_percentage: float = 0.5  # If use_points_with_overlap is activated, what percentage of points should actually overlap with each other in the (with_overlap, with_overlap) case
    use_comparison_as_teacher: bool = False  # Use student-teacher loss to train embeddings

    embedding_loss_size_differentiation_enabled: bool = False
    embedding_loss_size_differentiation_threshold: float = EMBEDDING_LOSS_SIZE_DIFFERENTIATION_THRESHOLD_DEFAULT  # 1.35 comes from the limits we use when generating comparison image pairs for training

    # Dataset generation size limits
    validation_set_size_limit: Optional[int] = None
    test_set_size_limit: Optional[int] = None
    train_set_size_limit: Optional[int] = None

    modify_dataset: bool = False

    train_min_average_crop_size_mm: Optional[float] = None
    train_max_average_crop_size_mm: Optional[float] = None

    validation_min_average_crop_size_mm: Optional[float] = None
    validation_max_average_crop_size_mm: Optional[float] = None

    test_min_average_crop_size_mm: Optional[float] = None
    test_max_average_crop_size_mm: Optional[float] = None

    enable_crop_embeddings: bool = False
    dataset_version: int = DEFAULT_DATASET_VERSION  # 1: Dataset v1, 2: Dataset v2
    check_val_every_n_epoch: int = 1

    train_default_crop: Optional[float] = None
    use_embedding_head: bool = False
