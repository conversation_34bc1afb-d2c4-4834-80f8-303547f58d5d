# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: startup_task/startup_task.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'startup_task/startup_task.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstartup_task/startup_task.proto\x12\x13\x63\x61rbon.startup_task\"@\n\x18\x43rosshairCalibrationTask\x12\x12\n\nrow_number\x18\x01 \x01(\r\x12\x10\n\x08laser_id\x18\x02 \x01(\r\"\xc2\x01\n\x04Task\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05label\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12-\n\x05state\x18\x04 \x01(\x0e\x32\x1e.carbon.startup_task.TaskState\x12K\n\x12\x63rosshair_cal_task\x18\x05 \x01(\x0b\x32-.carbon.startup_task.CrosshairCalibrationTaskH\x00\x42\x0e\n\x0ctask_details*N\n\tTaskState\x12\n\n\x06QUEUED\x10\x00\x12\x0f\n\x0bIN_PROGRESS\x10\x01\x12\x0c\n\x08\x43OMPLETE\x10\x02\x12\t\n\x05\x45RROR\x10\x03\x12\x0b\n\x07UNKNOWN\x10\x04\x42\x46ZDgithub.com/carbonrobotics/protos/golang/generated/proto/startup_taskb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'startup_task.startup_task_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZDgithub.com/carbonrobotics/protos/golang/generated/proto/startup_task'
  _globals['_TASKSTATE']._serialized_start=319
  _globals['_TASKSTATE']._serialized_end=397
  _globals['_CROSSHAIRCALIBRATIONTASK']._serialized_start=56
  _globals['_CROSSHAIRCALIBRATIONTASK']._serialized_end=120
  _globals['_TASK']._serialized_start=123
  _globals['_TASK']._serialized_end=317
# @@protoc_insertion_point(module_scope)
