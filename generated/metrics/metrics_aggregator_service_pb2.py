# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: metrics/metrics_aggregator_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'metrics/metrics_aggregator_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(metrics/metrics_aggregator_service.proto\x12\x12metrics_aggregator\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x19\n\x0cPingResponse\x12\t\n\x01x\x18\x01 \x01(\r\"t\n\x07Metrics\x12\x39\n\x07metrics\x18\x01 \x03(\x0b\x32(.metrics_aggregator.Metrics.MetricsEntry\x1a.\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x13\n\x11GetMetricsRequest\"\xb7\x01\n\x12GetMetricsResponse\x12O\n\rdaily_metrics\x18\x01 \x03(\x0b\x32\x38.metrics_aggregator.GetMetricsResponse.DailyMetricsEntry\x1aP\n\x11\x44\x61ilyMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics:\x02\x38\x01\"-\n\x1d\x41\x63knowledgeDailyMetricRequest\x12\x0c\n\x04\x64\x61ys\x18\x01 \x03(\t\" \n\x1e\x41\x63knowledgeDailyMetricResponse\"\x16\n\x14GetJobMetricsRequest\"H\n\x15GetJobMetricsResponse\x12/\n\njobMetrics\x18\x01 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics2\xb0\x03\n\x18MetricsAggregatorService\x12K\n\x04Ping\x12\x1f.metrics_aggregator.PingRequest\x1a .metrics_aggregator.PingResponse\"\x00\x12]\n\nGetMetrics\x12%.metrics_aggregator.GetMetricsRequest\x1a&.metrics_aggregator.GetMetricsResponse\"\x00\x12\x81\x01\n\x16\x41\x63knowledgeDailyMetric\x12\x31.metrics_aggregator.AcknowledgeDailyMetricRequest\x1a\x32.metrics_aggregator.AcknowledgeDailyMetricResponse\"\x00\x12\x64\n\rGetJobMetrics\x12(.metrics_aggregator.GetJobMetricsRequest\x1a).metrics_aggregator.GetJobMetricsResponseBLZJgithub.com/carbonrobotics/protos/golang/generated/proto/metrics_aggregatorb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'metrics.metrics_aggregator_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZJgithub.com/carbonrobotics/protos/golang/generated/proto/metrics_aggregator'
  _globals['_METRICS_METRICSENTRY']._loaded_options = None
  _globals['_METRICS_METRICSENTRY']._serialized_options = b'8\001'
  _globals['_GETMETRICSRESPONSE_DAILYMETRICSENTRY']._loaded_options = None
  _globals['_GETMETRICSRESPONSE_DAILYMETRICSENTRY']._serialized_options = b'8\001'
  _globals['_PINGREQUEST']._serialized_start=64
  _globals['_PINGREQUEST']._serialized_end=88
  _globals['_PINGRESPONSE']._serialized_start=90
  _globals['_PINGRESPONSE']._serialized_end=115
  _globals['_METRICS']._serialized_start=117
  _globals['_METRICS']._serialized_end=233
  _globals['_METRICS_METRICSENTRY']._serialized_start=187
  _globals['_METRICS_METRICSENTRY']._serialized_end=233
  _globals['_GETMETRICSREQUEST']._serialized_start=235
  _globals['_GETMETRICSREQUEST']._serialized_end=254
  _globals['_GETMETRICSRESPONSE']._serialized_start=257
  _globals['_GETMETRICSRESPONSE']._serialized_end=440
  _globals['_GETMETRICSRESPONSE_DAILYMETRICSENTRY']._serialized_start=360
  _globals['_GETMETRICSRESPONSE_DAILYMETRICSENTRY']._serialized_end=440
  _globals['_ACKNOWLEDGEDAILYMETRICREQUEST']._serialized_start=442
  _globals['_ACKNOWLEDGEDAILYMETRICREQUEST']._serialized_end=487
  _globals['_ACKNOWLEDGEDAILYMETRICRESPONSE']._serialized_start=489
  _globals['_ACKNOWLEDGEDAILYMETRICRESPONSE']._serialized_end=521
  _globals['_GETJOBMETRICSREQUEST']._serialized_start=523
  _globals['_GETJOBMETRICSREQUEST']._serialized_end=545
  _globals['_GETJOBMETRICSRESPONSE']._serialized_start=547
  _globals['_GETJOBMETRICSRESPONSE']._serialized_end=619
  _globals['_METRICSAGGREGATORSERVICE']._serialized_start=622
  _globals['_METRICSAGGREGATORSERVICE']._serialized_end=1054
# @@protoc_insertion_point(module_scope)
