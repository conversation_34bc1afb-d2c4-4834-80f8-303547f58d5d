# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.metrics import metrics_aggregator_service_pb2 as metrics_dot_metrics__aggregator__service__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in metrics/metrics_aggregator_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class MetricsAggregatorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/Ping',
                request_serializer=metrics_dot_metrics__aggregator__service__pb2.PingRequest.SerializeToString,
                response_deserializer=metrics_dot_metrics__aggregator__service__pb2.PingResponse.FromString,
                _registered_method=True)
        self.GetMetrics = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/GetMetrics',
                request_serializer=metrics_dot_metrics__aggregator__service__pb2.GetMetricsRequest.SerializeToString,
                response_deserializer=metrics_dot_metrics__aggregator__service__pb2.GetMetricsResponse.FromString,
                _registered_method=True)
        self.AcknowledgeDailyMetric = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/AcknowledgeDailyMetric',
                request_serializer=metrics_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricRequest.SerializeToString,
                response_deserializer=metrics_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricResponse.FromString,
                _registered_method=True)
        self.GetJobMetrics = channel.unary_unary(
                '/metrics_aggregator.MetricsAggregatorService/GetJobMetrics',
                request_serializer=metrics_dot_metrics__aggregator__service__pb2.GetJobMetricsRequest.SerializeToString,
                response_deserializer=metrics_dot_metrics__aggregator__service__pb2.GetJobMetricsResponse.FromString,
                _registered_method=True)


class MetricsAggregatorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AcknowledgeDailyMetric(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetJobMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_MetricsAggregatorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=metrics_dot_metrics__aggregator__service__pb2.PingRequest.FromString,
                    response_serializer=metrics_dot_metrics__aggregator__service__pb2.PingResponse.SerializeToString,
            ),
            'GetMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMetrics,
                    request_deserializer=metrics_dot_metrics__aggregator__service__pb2.GetMetricsRequest.FromString,
                    response_serializer=metrics_dot_metrics__aggregator__service__pb2.GetMetricsResponse.SerializeToString,
            ),
            'AcknowledgeDailyMetric': grpc.unary_unary_rpc_method_handler(
                    servicer.AcknowledgeDailyMetric,
                    request_deserializer=metrics_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricRequest.FromString,
                    response_serializer=metrics_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricResponse.SerializeToString,
            ),
            'GetJobMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetJobMetrics,
                    request_deserializer=metrics_dot_metrics__aggregator__service__pb2.GetJobMetricsRequest.FromString,
                    response_serializer=metrics_dot_metrics__aggregator__service__pb2.GetJobMetricsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'metrics_aggregator.MetricsAggregatorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('metrics_aggregator.MetricsAggregatorService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class MetricsAggregatorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/metrics_aggregator.MetricsAggregatorService/Ping',
            metrics_dot_metrics__aggregator__service__pb2.PingRequest.SerializeToString,
            metrics_dot_metrics__aggregator__service__pb2.PingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/metrics_aggregator.MetricsAggregatorService/GetMetrics',
            metrics_dot_metrics__aggregator__service__pb2.GetMetricsRequest.SerializeToString,
            metrics_dot_metrics__aggregator__service__pb2.GetMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AcknowledgeDailyMetric(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/metrics_aggregator.MetricsAggregatorService/AcknowledgeDailyMetric',
            metrics_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricRequest.SerializeToString,
            metrics_dot_metrics__aggregator__service__pb2.AcknowledgeDailyMetricResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetJobMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/metrics_aggregator.MetricsAggregatorService/GetJobMetrics',
            metrics_dot_metrics__aggregator__service__pb2.GetJobMetricsRequest.SerializeToString,
            metrics_dot_metrics__aggregator__service__pb2.GetJobMetricsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
