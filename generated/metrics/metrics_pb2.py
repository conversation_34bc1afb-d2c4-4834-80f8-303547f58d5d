# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: metrics/metrics.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'metrics/metrics.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15metrics/metrics.proto\x12\x0e\x63\x61rbon.metrics\"*\n\rLaserPosition\x12\x0b\n\x03row\x18\x01 \x01(\r\x12\x0c\n\x04slot\x18\x02 \x01(\r\"R\n\x0fLaserIdentifier\x12/\n\x08position\x18\x01 \x01(\x0b\x32\x1d.carbon.metrics.LaserPosition\x12\x0e\n\x06serial\x18\x02 \x01(\t\"R\n\rLaserLifeTime\x12+\n\x02id\x18\x01 \x01(\x0b\x32\x1f.carbon.metrics.LaserIdentifier\x12\x14\n\x0clifetime_sec\x18\x02 \x01(\x04\"T\n\x0eLaserEventTime\x12+\n\x02id\x18\x01 \x01(\x0b\x32\x1f.carbon.metrics.LaserIdentifier\x12\x15\n\rtimestamp_sec\x18\x02 \x01(\x03\"B\n\x0eLaserLifeTimes\x12\x30\n\tlifetimes\x18\x01 \x03(\x0b\x32\x1d.carbon.metrics.LaserLifeTime\"v\n\x10LaserChangeTimes\x12\x30\n\x08installs\x18\x01 \x03(\x0b\x32\x1e.carbon.metrics.LaserEventTime\x12\x30\n\x08removals\x18\x02 \x03(\x0b\x32\x1e.carbon.metrics.LaserEventTime\"n\n\x16\x43ountsByConclusionType\x12\x15\n\rdisarmed_weed\x18\x01 \x03(\r\x12\x12\n\narmed_weed\x18\x02 \x03(\r\x12\x15\n\rdisarmed_crop\x18\x03 \x03(\r\x12\x12\n\narmed_crop\x18\x04 \x03(\r\"8\n\x0eTargetSizeData\x12\x17\n\x0f\x63umulative_size\x18\x01 \x01(\x01\x12\r\n\x05\x63ount\x18\x02 \x01(\x04\"?\n\x15RequiredLaserTimeData\x12\x17\n\x0f\x63umulative_time\x18\x01 \x01(\x04\x12\r\n\x05\x63ount\x18\x02 \x01(\x04\"\x8f\x01\n\x0fSpatialPosition\x12\x10\n\x08latitude\x18\x01 \x01(\x01\x12\x11\n\tlongitude\x18\x02 \x01(\x01\x12\x11\n\theight_mm\x18\x03 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x04\x12\x0e\n\x06\x65\x63\x65\x66_x\x18\x05 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_y\x18\x06 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_z\x18\x07 \x01(\x01\"\x80\x04\n\x10WeedCounterChunk\x12\x41\n\x11\x63onclusion_counts\x18\x01 \x01(\x0b\x32&.carbon.metrics.CountsByConclusionType\x12\x36\n\x0eweed_size_data\x18\x02 \x01(\x0b\x32\x1e.carbon.metrics.TargetSizeData\x12\x36\n\x0e\x63rop_size_data\x18\x03 \x01(\x0b\x32\x1e.carbon.metrics.TargetSizeData\x12R\n\x12\x63ounts_by_category\x18\x04 \x03(\x0b\x32\x36.carbon.metrics.WeedCounterChunk.CountsByCategoryEntry\x12G\n\x18targeted_laser_time_data\x18\x05 \x01(\x0b\x32%.carbon.metrics.RequiredLaserTimeData\x12I\n\x1auntargeted_laser_time_data\x18\x06 \x01(\x0b\x32%.carbon.metrics.RequiredLaserTimeData\x12\x18\n\x10valid_crop_count\x18\x07 \x01(\x04\x1a\x37\n\x15\x43ountsByCategoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\r:\x02\x38\x01\"A\n\x17WheelEncoderSpatialData\x12\x13\n\x0bstart_pos_m\x18\x01 \x01(\x02\x12\x11\n\tend_pos_m\x18\x02 \x01(\x02\",\n\x12\x42\x61ndingSpatialData\x12\x16\n\x0epercent_banded\x18\x01 \x01(\x02\"-\n\x19ImplementWidthSpatialData\x12\x10\n\x08width_mm\x18\x01 \x01(\x02\"\x9d\x01\n\x15VelocitySpatialMetric\x12O\n\x0e\x61vg_target_vel\x18\x01 \x03(\x0b\x32\x37.carbon.metrics.VelocitySpatialMetric.AvgTargetVelEntry\x1a\x33\n\x11\x41vgTargetVelEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\x1b\n\tJobMetric\x12\x0e\n\x06job_id\x18\x01 \x01(\t\"i\n\x08HWMetric\x12\x0e\n\x06lifted\x18\x01 \x01(\x08\x12\x10\n\x08\x65stopped\x18\x02 \x01(\x08\x12\x11\n\tlaser_key\x18\x03 \x01(\x08\x12\x11\n\tinterlock\x18\x04 \x01(\x08\x12\x15\n\rwater_protect\x18\x05 \x01(\x08\"\xe0\x05\n\x12SpatialMetricBlock\x12.\n\x05start\x18\x01 \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12,\n\x03\x65nd\x18\x02 \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x34\n\nweed_count\x18\x03 \x01(\x0b\x32 .carbon.metrics.WeedCounterChunk\x12\x38\n\x07we_data\x18\x04 \x01(\x0b\x32\'.carbon.metrics.WheelEncoderSpatialData\x12\x38\n\x0c\x62\x61nding_data\x18\x05 \x01(\x0b\x32\".carbon.metrics.BandingSpatialData\x12G\n\x14implement_width_data\x18\x06 \x01(\x0b\x32).carbon.metrics.ImplementWidthSpatialData\x12\x37\n\x08vel_data\x18\x07 \x01(\x0b\x32%.carbon.metrics.VelocitySpatialMetric\x12\x33\n\nstart_left\x18\x08 \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x34\n\x0bstart_right\x18\t \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x31\n\x08\x65nd_left\x18\n \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12\x32\n\tend_right\x18\x0b \x01(\x0b\x32\x1f.carbon.metrics.SpatialPosition\x12-\n\njob_metric\x18\x0c \x01(\x0b\x32\x19.carbon.metrics.JobMetric\x12\x12\n\nsuspicious\x18\r \x01(\x08\x12+\n\thw_metric\x18\x0e \x01(\x0b\x32\x18.carbon.metrics.HWMetricBAZ?github.com/carbonrobotics/protos/golang/generated/proto/metricsb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'metrics.metrics_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z?github.com/carbonrobotics/protos/golang/generated/proto/metrics'
  _globals['_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY']._loaded_options = None
  _globals['_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY']._serialized_options = b'8\001'
  _globals['_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY']._loaded_options = None
  _globals['_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY']._serialized_options = b'8\001'
  _globals['_LASERPOSITION']._serialized_start=41
  _globals['_LASERPOSITION']._serialized_end=83
  _globals['_LASERIDENTIFIER']._serialized_start=85
  _globals['_LASERIDENTIFIER']._serialized_end=167
  _globals['_LASERLIFETIME']._serialized_start=169
  _globals['_LASERLIFETIME']._serialized_end=251
  _globals['_LASEREVENTTIME']._serialized_start=253
  _globals['_LASEREVENTTIME']._serialized_end=337
  _globals['_LASERLIFETIMES']._serialized_start=339
  _globals['_LASERLIFETIMES']._serialized_end=405
  _globals['_LASERCHANGETIMES']._serialized_start=407
  _globals['_LASERCHANGETIMES']._serialized_end=525
  _globals['_COUNTSBYCONCLUSIONTYPE']._serialized_start=527
  _globals['_COUNTSBYCONCLUSIONTYPE']._serialized_end=637
  _globals['_TARGETSIZEDATA']._serialized_start=639
  _globals['_TARGETSIZEDATA']._serialized_end=695
  _globals['_REQUIREDLASERTIMEDATA']._serialized_start=697
  _globals['_REQUIREDLASERTIMEDATA']._serialized_end=760
  _globals['_SPATIALPOSITION']._serialized_start=763
  _globals['_SPATIALPOSITION']._serialized_end=906
  _globals['_WEEDCOUNTERCHUNK']._serialized_start=909
  _globals['_WEEDCOUNTERCHUNK']._serialized_end=1421
  _globals['_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY']._serialized_start=1366
  _globals['_WEEDCOUNTERCHUNK_COUNTSBYCATEGORYENTRY']._serialized_end=1421
  _globals['_WHEELENCODERSPATIALDATA']._serialized_start=1423
  _globals['_WHEELENCODERSPATIALDATA']._serialized_end=1488
  _globals['_BANDINGSPATIALDATA']._serialized_start=1490
  _globals['_BANDINGSPATIALDATA']._serialized_end=1534
  _globals['_IMPLEMENTWIDTHSPATIALDATA']._serialized_start=1536
  _globals['_IMPLEMENTWIDTHSPATIALDATA']._serialized_end=1581
  _globals['_VELOCITYSPATIALMETRIC']._serialized_start=1584
  _globals['_VELOCITYSPATIALMETRIC']._serialized_end=1741
  _globals['_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY']._serialized_start=1690
  _globals['_VELOCITYSPATIALMETRIC_AVGTARGETVELENTRY']._serialized_end=1741
  _globals['_JOBMETRIC']._serialized_start=1743
  _globals['_JOBMETRIC']._serialized_end=1770
  _globals['_HWMETRIC']._serialized_start=1772
  _globals['_HWMETRIC']._serialized_end=1877
  _globals['_SPATIALMETRICBLOCK']._serialized_start=1880
  _globals['_SPATIALMETRICBLOCK']._serialized_end=2616
# @@protoc_insertion_point(module_scope)
