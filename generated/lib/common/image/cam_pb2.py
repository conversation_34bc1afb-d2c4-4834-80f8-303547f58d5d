# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: lib/common/image/cam.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'lib/common/image/cam.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.common.buffer import buffer_pb2 as lib_dot_common_dot_buffer_dot_buffer__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1alib/common/image/cam.proto\x12\x10lib.common.image\x1a\x1elib/common/buffer/buffer.proto\"%\n\x04Size\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\"\xe4\x01\n\rCamImageProto\x12\x13\n\x0b\x64\x65vice_path\x18\x01 \x01(\t\x12\x11\n\tcamera_id\x18\x02 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x03 \x01(\x04\x12$\n\x04size\x18\x04 \x01(\x0b\x32\x16.lib.common.image.Size\x12-\n\x05\x62ytes\x18\x05 \x01(\x0b\x32\x1e.lib.common.buffer.BufferProto\x12\x33\n\x0b\x64\x65pth_bytes\x18\x06 \x01(\x0b\x32\x1e.lib.common.buffer.BufferProto\x12\x0b\n\x03ppi\x18\x07 \x01(\x02\x42JZHgithub.com/carbonrobotics/protos/golang/generated/proto/lib/common/imageb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lib.common.image.cam_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZHgithub.com/carbonrobotics/protos/golang/generated/proto/lib/common/image'
  _globals['_SIZE']._serialized_start=80
  _globals['_SIZE']._serialized_end=117
  _globals['_CAMIMAGEPROTO']._serialized_start=120
  _globals['_CAMIMAGEPROTO']._serialized_end=348
# @@protoc_insertion_point(module_scope)
