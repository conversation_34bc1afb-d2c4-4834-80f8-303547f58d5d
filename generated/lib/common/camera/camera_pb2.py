# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: lib/common/camera/camera.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'lib/common/camera/camera.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1elib/common/camera/camera.proto\x12\x11lib.common.camera*Y\n\x11LightSourcePreset\x12\x08\n\x04kOff\x10\x00\x12\x12\n\x0ekDaylight5000K\x10\x01\x12\x12\n\x0ekDaylight6500K\x10\x02\x12\x12\n\x0ekTungsten2800K\x10\x03\x42<Z:github.com/carbonrobotics/protos/golang/generated/proto/cvb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lib.common.camera.camera_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z:github.com/carbonrobotics/protos/golang/generated/proto/cv'
  _globals['_LIGHTSOURCEPRESET']._serialized_start=53
  _globals['_LIGHTSOURCEPRESET']._serialized_end=142
# @@protoc_insertion_point(module_scope)
