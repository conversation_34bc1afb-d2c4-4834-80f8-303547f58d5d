# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: lib/common/buffer/inline/inline.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'lib/common/buffer/inline/inline.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%lib/common/buffer/inline/inline.proto\x12\x11lib.common.buffer\"4\n\x11InlineBufferProto\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x12\x11\n\tnot_empty\x18\x02 \x01(\x08\x42KZIgithub.com/carbonrobotics/protos/golang/generated/proto/lib/common/bufferb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lib.common.buffer.inline.inline_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZIgithub.com/carbonrobotics/protos/golang/generated/proto/lib/common/buffer'
  _globals['_INLINEBUFFERPROTO']._serialized_start=60
  _globals['_INLINEBUFFERPROTO']._serialized_end=112
# @@protoc_insertion_point(module_scope)
