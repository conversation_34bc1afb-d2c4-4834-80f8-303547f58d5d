# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: lib/common/buffer/buffer.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'lib/common/buffer/buffer.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.lib.common.buffer.inline import inline_pb2 as lib_dot_common_dot_buffer_dot_inline_dot_inline__pb2
from generated.lib.common.buffer.shm import shm_pb2 as lib_dot_common_dot_buffer_dot_shm_dot_shm__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1elib/common/buffer/buffer.proto\x12\x11lib.common.buffer\x1a%lib/common/buffer/inline/inline.proto\x1a\x1flib/common/buffer/shm/shm.proto\"\xb7\x01\n\x0b\x42ufferProto\x12\x36\n\x06inline\x18\x01 \x01(\x0b\x32$.lib.common.buffer.InlineBufferProtoH\x00\x12\x32\n\x07memfile\x18\x02 \x01(\x0b\x32\x1f.lib.common.buffer.MemFileProtoH\x00\x12\x34\n\x05shmem\x18\x03 \x01(\x0b\x32#.lib.common.buffer.ShmemBufferProtoH\x00\x42\x06\n\x04typeBKZIgithub.com/carbonrobotics/protos/golang/generated/proto/lib/common/bufferb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'lib.common.buffer.buffer_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZIgithub.com/carbonrobotics/protos/golang/generated/proto/lib/common/buffer'
  _globals['_BUFFERPROTO']._serialized_start=126
  _globals['_BUFFERPROTO']._serialized_end=309
# @@protoc_insertion_point(module_scope)
