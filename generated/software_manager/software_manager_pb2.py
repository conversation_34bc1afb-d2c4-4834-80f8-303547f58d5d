# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: software_manager/software_manager.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'software_manager/software_manager.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'software_manager/software_manager.proto\x12\x17\x63\x61rbon.software_manager\"l\n\x17SoftwareVersionMetadata\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x12\n\ncontainers\x18\x02 \x03(\t\x12\x0e\n\x06system\x18\x03 \x01(\t\x12\r\n\x05ready\x18\x04 \x01(\x08\x12\x11\n\tavailable\x18\x05 \x01(\x08\"-\n\x1eSoftwareVersionMetadataRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\"\x17\n\x15VersionSummaryRequest\"\xde\x01\n\x13VersionSummaryReply\x12\x41\n\x07\x63urrent\x18\x01 \x01(\x0b\x32\x30.carbon.software_manager.SoftwareVersionMetadata\x12@\n\x06target\x18\x02 \x01(\x0b\x32\x30.carbon.software_manager.SoftwareVersionMetadata\x12\x42\n\x08previous\x18\x03 \x01(\x0b\x32\x30.carbon.software_manager.SoftwareVersionMetadata\"#\n\x14TriggerUpdateRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\"\x14\n\x12TriggerUpdateReply\"\x14\n\x12GetIdentityRequest\")\n\x0c\x43omputerRole\x12\x0c\n\x04role\x18\x01 \x01(\t\x12\x0b\n\x03row\x18\x02 \x01(\t\"\x81\x02\n\x0cIdentityInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ngeneration\x18\x02 \x01(\t\x12\x33\n\x04role\x18\x03 \x01(\x0b\x32%.carbon.software_manager.ComputerRole\x12\x16\n\x0e\x61uth_client_id\x18\x04 \x01(\t\x12\x1a\n\x12\x61uth_client_secret\x18\x05 \x01(\t\x12\x13\n\x0b\x61uth_domain\x18\x06 \x01(\t\x12\x1d\n\x15\x63\x61rbon_robot_username\x18\x07 \x01(\t\x12\x1d\n\x15\x63\x61rbon_robot_password\x18\x08 \x01(\t\x12\x13\n\x0b\x65nvironment\x18\t \x01(\t\"\x1b\n\x19\x43learPackagesCacheRequest\"\x1c\n\x1a\x43learPackagesCacheResponse\"#\n\x14PrepareUpdateRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\"\x17\n\x15PrepareUpdateResponse\"!\n\x12\x41\x62ortUpdateRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\"\x15\n\x13\x41\x62ortUpdateResponse2\xbf\x06\n\x16SoftwareManagerService\x12\x87\x01\n\x1aGetSoftwareVersionMetadata\x12\x37.carbon.software_manager.SoftwareVersionMetadataRequest\x1a\x30.carbon.software_manager.SoftwareVersionMetadata\x12r\n\x12GetVersionsSummary\x12..carbon.software_manager.VersionSummaryRequest\x1a,.carbon.software_manager.VersionSummaryReply\x12k\n\rTriggerUpdate\x12-.carbon.software_manager.TriggerUpdateRequest\x1a+.carbon.software_manager.TriggerUpdateReply\x12\x61\n\x0bGetIdentity\x12+.carbon.software_manager.GetIdentityRequest\x1a%.carbon.software_manager.IdentityInfo\x12}\n\x12\x43learPackagesCache\x12\x32.carbon.software_manager.ClearPackagesCacheRequest\x1a\x33.carbon.software_manager.ClearPackagesCacheResponse\x12n\n\rPrepareUpdate\x12-.carbon.software_manager.PrepareUpdateRequest\x1a..carbon.software_manager.PrepareUpdateResponse\x12h\n\x0b\x41\x62ortUpdate\x12+.carbon.software_manager.AbortUpdateRequest\x1a,.carbon.software_manager.AbortUpdateResponseBJZHgithub.com/carbonrobotics/protos/golang/generated/proto/software_managerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'software_manager.software_manager_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZHgithub.com/carbonrobotics/protos/golang/generated/proto/software_manager'
  _globals['_SOFTWAREVERSIONMETADATA']._serialized_start=68
  _globals['_SOFTWAREVERSIONMETADATA']._serialized_end=176
  _globals['_SOFTWAREVERSIONMETADATAREQUEST']._serialized_start=178
  _globals['_SOFTWAREVERSIONMETADATAREQUEST']._serialized_end=223
  _globals['_VERSIONSUMMARYREQUEST']._serialized_start=225
  _globals['_VERSIONSUMMARYREQUEST']._serialized_end=248
  _globals['_VERSIONSUMMARYREPLY']._serialized_start=251
  _globals['_VERSIONSUMMARYREPLY']._serialized_end=473
  _globals['_TRIGGERUPDATEREQUEST']._serialized_start=475
  _globals['_TRIGGERUPDATEREQUEST']._serialized_end=510
  _globals['_TRIGGERUPDATEREPLY']._serialized_start=512
  _globals['_TRIGGERUPDATEREPLY']._serialized_end=532
  _globals['_GETIDENTITYREQUEST']._serialized_start=534
  _globals['_GETIDENTITYREQUEST']._serialized_end=554
  _globals['_COMPUTERROLE']._serialized_start=556
  _globals['_COMPUTERROLE']._serialized_end=597
  _globals['_IDENTITYINFO']._serialized_start=600
  _globals['_IDENTITYINFO']._serialized_end=857
  _globals['_CLEARPACKAGESCACHEREQUEST']._serialized_start=859
  _globals['_CLEARPACKAGESCACHEREQUEST']._serialized_end=886
  _globals['_CLEARPACKAGESCACHERESPONSE']._serialized_start=888
  _globals['_CLEARPACKAGESCACHERESPONSE']._serialized_end=916
  _globals['_PREPAREUPDATEREQUEST']._serialized_start=918
  _globals['_PREPAREUPDATEREQUEST']._serialized_end=953
  _globals['_PREPAREUPDATERESPONSE']._serialized_start=955
  _globals['_PREPAREUPDATERESPONSE']._serialized_end=978
  _globals['_ABORTUPDATEREQUEST']._serialized_start=980
  _globals['_ABORTUPDATEREQUEST']._serialized_end=1013
  _globals['_ABORTUPDATERESPONSE']._serialized_start=1015
  _globals['_ABORTUPDATERESPONSE']._serialized_end=1036
  _globals['_SOFTWAREMANAGERSERVICE']._serialized_start=1039
  _globals['_SOFTWAREMANAGERSERVICE']._serialized_end=1870
# @@protoc_insertion_point(module_scope)
