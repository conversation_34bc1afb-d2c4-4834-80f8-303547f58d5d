# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.software_manager import software_manager_pb2 as software__manager_dot_software__manager__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in software_manager/software_manager_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class SoftwareManagerServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetSoftwareVersionMetadata = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/GetSoftwareVersionMetadata',
                request_serializer=software__manager_dot_software__manager__pb2.SoftwareVersionMetadataRequest.SerializeToString,
                response_deserializer=software__manager_dot_software__manager__pb2.SoftwareVersionMetadata.FromString,
                _registered_method=True)
        self.GetVersionsSummary = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/GetVersionsSummary',
                request_serializer=software__manager_dot_software__manager__pb2.VersionSummaryRequest.SerializeToString,
                response_deserializer=software__manager_dot_software__manager__pb2.VersionSummaryReply.FromString,
                _registered_method=True)
        self.TriggerUpdate = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/TriggerUpdate',
                request_serializer=software__manager_dot_software__manager__pb2.TriggerUpdateRequest.SerializeToString,
                response_deserializer=software__manager_dot_software__manager__pb2.TriggerUpdateReply.FromString,
                _registered_method=True)
        self.GetIdentity = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/GetIdentity',
                request_serializer=software__manager_dot_software__manager__pb2.GetIdentityRequest.SerializeToString,
                response_deserializer=software__manager_dot_software__manager__pb2.IdentityInfo.FromString,
                _registered_method=True)
        self.ClearPackagesCache = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/ClearPackagesCache',
                request_serializer=software__manager_dot_software__manager__pb2.ClearPackagesCacheRequest.SerializeToString,
                response_deserializer=software__manager_dot_software__manager__pb2.ClearPackagesCacheResponse.FromString,
                _registered_method=True)
        self.PrepareUpdate = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/PrepareUpdate',
                request_serializer=software__manager_dot_software__manager__pb2.PrepareUpdateRequest.SerializeToString,
                response_deserializer=software__manager_dot_software__manager__pb2.PrepareUpdateResponse.FromString,
                _registered_method=True)
        self.AbortUpdate = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/AbortUpdate',
                request_serializer=software__manager_dot_software__manager__pb2.AbortUpdateRequest.SerializeToString,
                response_deserializer=software__manager_dot_software__manager__pb2.AbortUpdateResponse.FromString,
                _registered_method=True)


class SoftwareManagerServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetSoftwareVersionMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetVersionsSummary(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TriggerUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIdentity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearPackagesCache(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PrepareUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AbortUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SoftwareManagerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetSoftwareVersionMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSoftwareVersionMetadata,
                    request_deserializer=software__manager_dot_software__manager__pb2.SoftwareVersionMetadataRequest.FromString,
                    response_serializer=software__manager_dot_software__manager__pb2.SoftwareVersionMetadata.SerializeToString,
            ),
            'GetVersionsSummary': grpc.unary_unary_rpc_method_handler(
                    servicer.GetVersionsSummary,
                    request_deserializer=software__manager_dot_software__manager__pb2.VersionSummaryRequest.FromString,
                    response_serializer=software__manager_dot_software__manager__pb2.VersionSummaryReply.SerializeToString,
            ),
            'TriggerUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.TriggerUpdate,
                    request_deserializer=software__manager_dot_software__manager__pb2.TriggerUpdateRequest.FromString,
                    response_serializer=software__manager_dot_software__manager__pb2.TriggerUpdateReply.SerializeToString,
            ),
            'GetIdentity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIdentity,
                    request_deserializer=software__manager_dot_software__manager__pb2.GetIdentityRequest.FromString,
                    response_serializer=software__manager_dot_software__manager__pb2.IdentityInfo.SerializeToString,
            ),
            'ClearPackagesCache': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearPackagesCache,
                    request_deserializer=software__manager_dot_software__manager__pb2.ClearPackagesCacheRequest.FromString,
                    response_serializer=software__manager_dot_software__manager__pb2.ClearPackagesCacheResponse.SerializeToString,
            ),
            'PrepareUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.PrepareUpdate,
                    request_deserializer=software__manager_dot_software__manager__pb2.PrepareUpdateRequest.FromString,
                    response_serializer=software__manager_dot_software__manager__pb2.PrepareUpdateResponse.SerializeToString,
            ),
            'AbortUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.AbortUpdate,
                    request_deserializer=software__manager_dot_software__manager__pb2.AbortUpdateRequest.FromString,
                    response_serializer=software__manager_dot_software__manager__pb2.AbortUpdateResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.software_manager.SoftwareManagerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.software_manager.SoftwareManagerService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class SoftwareManagerService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetSoftwareVersionMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.software_manager.SoftwareManagerService/GetSoftwareVersionMetadata',
            software__manager_dot_software__manager__pb2.SoftwareVersionMetadataRequest.SerializeToString,
            software__manager_dot_software__manager__pb2.SoftwareVersionMetadata.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetVersionsSummary(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.software_manager.SoftwareManagerService/GetVersionsSummary',
            software__manager_dot_software__manager__pb2.VersionSummaryRequest.SerializeToString,
            software__manager_dot_software__manager__pb2.VersionSummaryReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TriggerUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.software_manager.SoftwareManagerService/TriggerUpdate',
            software__manager_dot_software__manager__pb2.TriggerUpdateRequest.SerializeToString,
            software__manager_dot_software__manager__pb2.TriggerUpdateReply.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIdentity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.software_manager.SoftwareManagerService/GetIdentity',
            software__manager_dot_software__manager__pb2.GetIdentityRequest.SerializeToString,
            software__manager_dot_software__manager__pb2.IdentityInfo.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ClearPackagesCache(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.software_manager.SoftwareManagerService/ClearPackagesCache',
            software__manager_dot_software__manager__pb2.ClearPackagesCacheRequest.SerializeToString,
            software__manager_dot_software__manager__pb2.ClearPackagesCacheResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PrepareUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.software_manager.SoftwareManagerService/PrepareUpdate',
            software__manager_dot_software__manager__pb2.PrepareUpdateRequest.SerializeToString,
            software__manager_dot_software__manager__pb2.PrepareUpdateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AbortUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.software_manager.SoftwareManagerService/AbortUpdate',
            software__manager_dot_software__manager__pb2.AbortUpdateRequest.SerializeToString,
            software__manager_dot_software__manager__pb2.AbortUpdateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
