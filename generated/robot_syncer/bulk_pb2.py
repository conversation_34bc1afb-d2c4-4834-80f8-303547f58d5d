# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: robot_syncer/bulk.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'robot_syncer/bulk.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from generated.config.api import config_service_pb2 as config_dot_api_dot_config__service__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17robot_syncer/bulk.proto\x12\x18\x63\x61rbon.robot_syncer.bulk\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1f\x63onfig/api/config_service.proto\"\x9b\x01\n\x0f\x42ulkEditRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12-\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07serials\x18\x03 \x03(\t\x12\x37\n\noperations\x18\x04 \x03(\x0b\x32#.carbon.robot_syncer.bulk.Operation\"r\n\tOperation\x12\x33\n\x08key_spec\x18\x01 \x01(\x0b\x32!.carbon.robot_syncer.bulk.KeySpec\x12\x30\n\x06\x61\x63tion\x18\x02 \x01(\x0b\x32 .carbon.robot_syncer.bulk.Action\"\xb4\x01\n\x06\x41\x63tion\x12\x32\n\x03set\x18\x01 \x01(\x0b\x32#.carbon.robot_syncer.bulk.SetActionH\x00\x12\x32\n\x03\x61\x64\x64\x18\x02 \x01(\x0b\x32#.carbon.robot_syncer.bulk.AddActionH\x00\x12\x38\n\x06remove\x18\x03 \x01(\x0b\x32&.carbon.robot_syncer.bulk.RemoveActionH\x00\x42\x08\n\x06\x61\x63tion\"<\n\tSetAction\x12/\n\x05value\x18\x01 \x01(\x0b\x32 .carbon.config.proto.ConfigValue\"\x19\n\tAddAction\x12\x0c\n\x04name\x18\x01 \x01(\t\"\x1c\n\x0cRemoveAction\x12\x0c\n\x04name\x18\x01 \x01(\t\"E\n\x07KeySpec\x12:\n\ncomponents\x18\x01 \x03(\x0b\x32&.carbon.robot_syncer.bulk.KeyComponent\"K\n\x0cKeyComponent\x12;\n\x08\x62ranches\x18\x01 \x03(\x0b\x32).carbon.robot_syncer.bulk.ComponentBranch\"e\n\x0f\x43omponentBranch\x12\x11\n\x07literal\x18\x01 \x01(\tH\x00\x12\x35\n\x08wildcard\x18\x02 \x01(\x0b\x32!.carbon.robot_syncer.bulk.PatternH\x00\x42\x08\n\x06\x62ranch\")\n\x07Pattern\x12\x0e\n\x06prefix\x18\x01 \x01(\t\x12\x0e\n\x06suffix\x18\x02 \x01(\t\"M\n\x10\x42ulkEditResponse\x12\x39\n\x06robots\x18\x01 \x03(\x0b\x32).carbon.robot_syncer.bulk.RobotEditRecord\"i\n\x0fRobotEditRecord\x12\x0e\n\x06serial\x18\x01 \x01(\t\x12\x35\n\x07records\x18\x02 \x03(\x0b\x32$.carbon.robot_syncer.bulk.EditRecord\x12\x0f\n\x07message\x18\x03 \x01(\t\"\x90\x01\n\nEditRecord\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x30\n\x06\x61\x63tion\x18\x02 \x01(\x0b\x32 .carbon.robot_syncer.bulk.Action\x12\x32\n\x07outcome\x18\x03 \x01(\x0e\x32!.carbon.robot_syncer.bulk.Outcome\x12\x0f\n\x07message\x18\x04 \x01(\t*<\n\x07Outcome\x12\x17\n\x13OUTCOME_UNSPECIFIED\x10\x00\x12\x0b\n\x07SUCCESS\x10\x01\x12\x0b\n\x07\x46\x41ILURE\x10\x02\x42\x46ZDgithub.com/carbonrobotics/protos/golang/generated/proto/robot_syncerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'robot_syncer.bulk_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZDgithub.com/carbonrobotics/protos/golang/generated/proto/robot_syncer'
  _globals['_OUTCOME']._serialized_start=1322
  _globals['_OUTCOME']._serialized_end=1382
  _globals['_BULKEDITREQUEST']._serialized_start=120
  _globals['_BULKEDITREQUEST']._serialized_end=275
  _globals['_OPERATION']._serialized_start=277
  _globals['_OPERATION']._serialized_end=391
  _globals['_ACTION']._serialized_start=394
  _globals['_ACTION']._serialized_end=574
  _globals['_SETACTION']._serialized_start=576
  _globals['_SETACTION']._serialized_end=636
  _globals['_ADDACTION']._serialized_start=638
  _globals['_ADDACTION']._serialized_end=663
  _globals['_REMOVEACTION']._serialized_start=665
  _globals['_REMOVEACTION']._serialized_end=693
  _globals['_KEYSPEC']._serialized_start=695
  _globals['_KEYSPEC']._serialized_end=764
  _globals['_KEYCOMPONENT']._serialized_start=766
  _globals['_KEYCOMPONENT']._serialized_end=841
  _globals['_COMPONENTBRANCH']._serialized_start=843
  _globals['_COMPONENTBRANCH']._serialized_end=944
  _globals['_PATTERN']._serialized_start=946
  _globals['_PATTERN']._serialized_end=987
  _globals['_BULKEDITRESPONSE']._serialized_start=989
  _globals['_BULKEDITRESPONSE']._serialized_end=1066
  _globals['_ROBOTEDITRECORD']._serialized_start=1068
  _globals['_ROBOTEDITRECORD']._serialized_end=1173
  _globals['_EDITRECORD']._serialized_start=1176
  _globals['_EDITRECORD']._serialized_end=1320
# @@protoc_insertion_point(module_scope)
