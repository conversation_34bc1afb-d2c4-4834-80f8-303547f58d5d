# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: robot_syncer/profile_sync.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'robot_syncer/profile_sync.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.frontend import profile_sync_pb2 as frontend_dot_profile__sync__pb2
from generated.almanac import almanac_pb2 as almanac_dot_almanac__pb2
from generated.frontend import banding_pb2 as frontend_dot_banding__pb2
from generated.thinning import thinning_pb2 as thinning_dot_thinning__pb2
from generated.target_velocity_estimator import target_velocity_estimator_pb2 as target__velocity__estimator_dot_target__velocity__estimator__pb2
from generated.category_profile import category_profile_pb2 as category__profile_dot_category__profile__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1frobot_syncer/profile_sync.proto\x12 carbon.robot_syncer.profile_sync\x1a\x0futil/util.proto\x1a\x1b\x66rontend/profile_sync.proto\x1a\x15\x61lmanac/almanac.proto\x1a\x16\x66rontend/banding.proto\x1a\x17thinning/thinning.proto\x1a\x39target_velocity_estimator/target_velocity_estimator.proto\x1a\'category_profile/category_profile.proto\"1\n\x19GetProfileSyncDataRequest\x12\x14\n\x0crobot_serial\x18\x01 \x01(\t\"\xda\x01\n\x1aGetProfileSyncDataResponse\x12\\\n\x08profiles\x18\x01 \x03(\x0b\x32J.carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry\x1a^\n\rProfilesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12<\n\x05value\x18\x02 \x01(\x0b\x32-.carbon.frontend.profile_sync.ProfileSyncData:\x02\x38\x01\"\xde\x04\n\x14UploadProfileRequest\x12\x1b\n\x13last_update_time_ms\x18\x01 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x02 \x01(\t\x12\x37\n\x07\x61lmanac\x18\x03 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfigH\x00\x12\x43\n\rdiscriminator\x18\x04 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfigH\x00\x12?\n\x0bmodelinator\x18\x05 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfigH\x00\x12\x36\n\x07\x62\x61nding\x18\x06 \x01(\x0b\x32#.carbon.frontend.banding.BandingDefH\x00\x12\x35\n\x08thinning\x18\x07 \x01(\x0b\x32!.carbon.thinning.ConfigDefinitionH\x00\x12X\n\x19target_velocity_estimator\x18\x08 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfileH\x00\x12I\n\x12\x63\x61tegoryCollection\x18\t \x01(\x0b\x32+.carbon.category_profile.CategoryCollectionH\x00\x12\x35\n\x08\x63\x61tegory\x18\n \x01(\x0b\x32!.carbon.category_profile.CategoryH\x00\x42\t\n\x07profile\"!\n\x11GetProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"\xa9\x04\n\x12GetProfileResponse\x12\x37\n\x07\x61lmanac\x18\x01 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfigH\x00\x12\x43\n\rdiscriminator\x18\x02 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfigH\x00\x12?\n\x0bmodelinator\x18\x03 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfigH\x00\x12\x36\n\x07\x62\x61nding\x18\x04 \x01(\x0b\x32#.carbon.frontend.banding.BandingDefH\x00\x12\x35\n\x08thinning\x18\x05 \x01(\x0b\x32!.carbon.thinning.ConfigDefinitionH\x00\x12X\n\x19target_velocity_estimator\x18\x06 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfileH\x00\x12I\n\x12\x63\x61tegoryCollection\x18\x07 \x01(\x0b\x32+.carbon.category_profile.CategoryCollectionH\x00\x12\x35\n\x08\x63\x61tegory\x18\x08 \x01(\x0b\x32!.carbon.category_profile.CategoryH\x00\x42\t\n\x07profile\"$\n\x14\x44\x65leteProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"#\n\x13PurgeProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t2\xb8\x04\n\x16RoSyProfileSyncService\x12\x8f\x01\n\x12GetProfileSyncData\x12;.carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest\x1a<.carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse\x12[\n\rUploadProfile\x12\x36.carbon.robot_syncer.profile_sync.UploadProfileRequest\x1a\x12.carbon.util.Empty\x12w\n\nGetProfile\x12\x33.carbon.robot_syncer.profile_sync.GetProfileRequest\x1a\x34.carbon.robot_syncer.profile_sync.GetProfileResponse\x12[\n\rDeleteProfile\x12\x36.carbon.robot_syncer.profile_sync.DeleteProfileRequest\x1a\x12.carbon.util.Empty\x12Y\n\x0cPurgeProfile\x12\x35.carbon.robot_syncer.profile_sync.PurgeProfileRequest\x1a\x12.carbon.util.EmptyBFZDgithub.com/carbonrobotics/protos/golang/generated/proto/robot_syncerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'robot_syncer.profile_sync_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZDgithub.com/carbonrobotics/protos/golang/generated/proto/robot_syncer'
  _globals['_GETPROFILESYNCDATARESPONSE_PROFILESENTRY']._loaded_options = None
  _globals['_GETPROFILESYNCDATARESPONSE_PROFILESENTRY']._serialized_options = b'8\001'
  _globals['_GETPROFILESYNCDATAREQUEST']._serialized_start=287
  _globals['_GETPROFILESYNCDATAREQUEST']._serialized_end=336
  _globals['_GETPROFILESYNCDATARESPONSE']._serialized_start=339
  _globals['_GETPROFILESYNCDATARESPONSE']._serialized_end=557
  _globals['_GETPROFILESYNCDATARESPONSE_PROFILESENTRY']._serialized_start=463
  _globals['_GETPROFILESYNCDATARESPONSE_PROFILESENTRY']._serialized_end=557
  _globals['_UPLOADPROFILEREQUEST']._serialized_start=560
  _globals['_UPLOADPROFILEREQUEST']._serialized_end=1166
  _globals['_GETPROFILEREQUEST']._serialized_start=1168
  _globals['_GETPROFILEREQUEST']._serialized_end=1201
  _globals['_GETPROFILERESPONSE']._serialized_start=1204
  _globals['_GETPROFILERESPONSE']._serialized_end=1757
  _globals['_DELETEPROFILEREQUEST']._serialized_start=1759
  _globals['_DELETEPROFILEREQUEST']._serialized_end=1795
  _globals['_PURGEPROFILEREQUEST']._serialized_start=1797
  _globals['_PURGEPROFILEREQUEST']._serialized_end=1832
  _globals['_ROSYPROFILESYNCSERVICE']._serialized_start=1835
  _globals['_ROSYPROFILESYNCSERVICE']._serialized_end=2403
# @@protoc_insertion_point(module_scope)
