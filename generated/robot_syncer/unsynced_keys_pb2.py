# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: robot_syncer/unsynced_keys.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'robot_syncer/unsynced_keys.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n robot_syncer/unsynced_keys.proto\x12!carbon.robot_syncer.unsynced_keys\"`\n\x0bUnsyncedKey\x12\x0f\n\x03key\x18\x01 \x01(\tB\x02\x18\x01\x12\x0e\n\x02ts\x18\x02 \x01(\x04\x42\x02\x18\x01\x12\x15\n\told_value\x18\x03 \x01(\tB\x02\x18\x01\x12\x15\n\tnew_value\x18\x04 \x01(\tB\x02\x18\x01:\x02\x18\x01\x42\x46ZDgithub.com/carbonrobotics/protos/golang/generated/proto/robot_syncerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'robot_syncer.unsynced_keys_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZDgithub.com/carbonrobotics/protos/golang/generated/proto/robot_syncer'
  _globals['_UNSYNCEDKEY'].fields_by_name['key']._loaded_options = None
  _globals['_UNSYNCEDKEY'].fields_by_name['key']._serialized_options = b'\030\001'
  _globals['_UNSYNCEDKEY'].fields_by_name['ts']._loaded_options = None
  _globals['_UNSYNCEDKEY'].fields_by_name['ts']._serialized_options = b'\030\001'
  _globals['_UNSYNCEDKEY'].fields_by_name['old_value']._loaded_options = None
  _globals['_UNSYNCEDKEY'].fields_by_name['old_value']._serialized_options = b'\030\001'
  _globals['_UNSYNCEDKEY'].fields_by_name['new_value']._loaded_options = None
  _globals['_UNSYNCEDKEY'].fields_by_name['new_value']._serialized_options = b'\030\001'
  _globals['_UNSYNCEDKEY']._loaded_options = None
  _globals['_UNSYNCEDKEY']._serialized_options = b'\030\001'
  _globals['_UNSYNCEDKEY']._serialized_start=71
  _globals['_UNSYNCEDKEY']._serialized_end=167
# @@protoc_insertion_point(module_scope)
