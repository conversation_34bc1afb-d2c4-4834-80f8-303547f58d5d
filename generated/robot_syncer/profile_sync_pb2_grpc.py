# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.robot_syncer import profile_sync_pb2 as robot__syncer_dot_profile__sync__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in robot_syncer/profile_sync_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class RoSyProfileSyncServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetProfileSyncData = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData',
                request_serializer=robot__syncer_dot_profile__sync__pb2.GetProfileSyncDataRequest.SerializeToString,
                response_deserializer=robot__syncer_dot_profile__sync__pb2.GetProfileSyncDataResponse.FromString,
                _registered_method=True)
        self.UploadProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile',
                request_serializer=robot__syncer_dot_profile__sync__pb2.UploadProfileRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile',
                request_serializer=robot__syncer_dot_profile__sync__pb2.GetProfileRequest.SerializeToString,
                response_deserializer=robot__syncer_dot_profile__sync__pb2.GetProfileResponse.FromString,
                _registered_method=True)
        self.DeleteProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile',
                request_serializer=robot__syncer_dot_profile__sync__pb2.DeleteProfileRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.PurgeProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile',
                request_serializer=robot__syncer_dot_profile__sync__pb2.PurgeProfileRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class RoSyProfileSyncServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetProfileSyncData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RoSyProfileSyncServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetProfileSyncData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfileSyncData,
                    request_deserializer=robot__syncer_dot_profile__sync__pb2.GetProfileSyncDataRequest.FromString,
                    response_serializer=robot__syncer_dot_profile__sync__pb2.GetProfileSyncDataResponse.SerializeToString,
            ),
            'UploadProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadProfile,
                    request_deserializer=robot__syncer_dot_profile__sync__pb2.UploadProfileRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfile,
                    request_deserializer=robot__syncer_dot_profile__sync__pb2.GetProfileRequest.FromString,
                    response_serializer=robot__syncer_dot_profile__sync__pb2.GetProfileResponse.SerializeToString,
            ),
            'DeleteProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProfile,
                    request_deserializer=robot__syncer_dot_profile__sync__pb2.DeleteProfileRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'PurgeProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeProfile,
                    request_deserializer=robot__syncer_dot_profile__sync__pb2.PurgeProfileRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.robot_syncer.profile_sync.RoSyProfileSyncService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.robot_syncer.profile_sync.RoSyProfileSyncService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class RoSyProfileSyncService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetProfileSyncData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData',
            robot__syncer_dot_profile__sync__pb2.GetProfileSyncDataRequest.SerializeToString,
            robot__syncer_dot_profile__sync__pb2.GetProfileSyncDataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UploadProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile',
            robot__syncer_dot_profile__sync__pb2.UploadProfileRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile',
            robot__syncer_dot_profile__sync__pb2.GetProfileRequest.SerializeToString,
            robot__syncer_dot_profile__sync__pb2.GetProfileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile',
            robot__syncer_dot_profile__sync__pb2.DeleteProfileRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PurgeProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile',
            robot__syncer_dot_profile__sync__pb2.PurgeProfileRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
