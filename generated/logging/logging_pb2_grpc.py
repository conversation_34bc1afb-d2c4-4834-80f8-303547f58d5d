# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.logging import logging_pb2 as logging_dot_logging__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in logging/logging_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class LoggingServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetLevel = channel.unary_unary(
                '/carbon.logging.LoggingService/SetLevel',
                request_serializer=logging_dot_logging__pb2.SetLevelRequest.SerializeToString,
                response_deserializer=logging_dot_logging__pb2.Empty.FromString,
                _registered_method=True)


class LoggingServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def SetLevel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LoggingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetLevel': grpc.unary_unary_rpc_method_handler(
                    servicer.SetLevel,
                    request_deserializer=logging_dot_logging__pb2.SetLevelRequest.FromString,
                    response_serializer=logging_dot_logging__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.logging.LoggingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.logging.LoggingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LoggingService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def SetLevel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.logging.LoggingService/SetLevel',
            logging_dot_logging__pb2.SetLevelRequest.SerializeToString,
            logging_dot_logging__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
