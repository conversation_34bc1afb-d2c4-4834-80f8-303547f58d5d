# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: logging/logging.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'logging/logging.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15logging/logging.proto\x12\x0e\x63\x61rbon.logging\"\x07\n\x05\x45mpty\"=\n\x0fSetLevelRequest\x12*\n\x08logLevel\x18\x01 \x01(\x0e\x32\x18.carbon.logging.LogLevel*X\n\x08LogLevel\x12\t\n\x05TRACE\x10\x00\x12\t\n\x05\x44\x45\x42UG\x10\x01\x12\x08\n\x04INFO\x10\x02\x12\x0b\n\x07WARNING\x10\x03\x12\t\n\x05\x45RROR\x10\x04\x12\t\n\x05\x46\x41TAL\x10\x05\x12\t\n\x05PANIC\x10\x06\x32T\n\x0eLoggingService\x12\x42\n\x08SetLevel\x12\x1f.carbon.logging.SetLevelRequest\x1a\x15.carbon.logging.EmptyBCH\x03Z?github.com/carbonrobotics/protos/golang/generated/proto/loggingb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'logging.logging_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'H\003Z?github.com/carbonrobotics/protos/golang/generated/proto/logging'
  _globals['_LOGLEVEL']._serialized_start=113
  _globals['_LOGLEVEL']._serialized_end=201
  _globals['_EMPTY']._serialized_start=41
  _globals['_EMPTY']._serialized_end=48
  _globals['_SETLEVELREQUEST']._serialized_start=50
  _globals['_SETLEVELREQUEST']._serialized_end=111
  _globals['_LOGGINGSERVICE']._serialized_start=203
  _globals['_LOGGINGSERVICE']._serialized_end=287
# @@protoc_insertion_point(module_scope)
