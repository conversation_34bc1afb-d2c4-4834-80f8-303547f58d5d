# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.model_manager import model_receiver_pb2 as model__manager_dot_model__receiver__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in model_manager/model_receiver_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ModelReceiverStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DownloadModel = channel.unary_unary(
                '/carbon.model_receiver.ModelReceiver/DownloadModel',
                request_serializer=model__manager_dot_model__receiver__pb2.DownloadModelRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetDownloadedModels = channel.unary_unary(
                '/carbon.model_receiver.ModelReceiver/GetDownloadedModels',
                request_serializer=util_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=model__manager_dot_model__receiver__pb2.DownloadedModelResponse.FromString,
                _registered_method=True)
        self.CleanupModels = channel.unary_unary(
                '/carbon.model_receiver.ModelReceiver/CleanupModels',
                request_serializer=model__manager_dot_model__receiver__pb2.CleanupModelsRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class ModelReceiverServicer(object):
    """Missing associated documentation comment in .proto file."""

    def DownloadModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDownloadedModels(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CleanupModels(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModelReceiverServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'DownloadModel': grpc.unary_unary_rpc_method_handler(
                    servicer.DownloadModel,
                    request_deserializer=model__manager_dot_model__receiver__pb2.DownloadModelRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetDownloadedModels': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDownloadedModels,
                    request_deserializer=util_dot_util__pb2.Empty.FromString,
                    response_serializer=model__manager_dot_model__receiver__pb2.DownloadedModelResponse.SerializeToString,
            ),
            'CleanupModels': grpc.unary_unary_rpc_method_handler(
                    servicer.CleanupModels,
                    request_deserializer=model__manager_dot_model__receiver__pb2.CleanupModelsRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.model_receiver.ModelReceiver', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.model_receiver.ModelReceiver', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ModelReceiver(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def DownloadModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.model_receiver.ModelReceiver/DownloadModel',
            model__manager_dot_model__receiver__pb2.DownloadModelRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDownloadedModels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.model_receiver.ModelReceiver/GetDownloadedModels',
            util_dot_util__pb2.Empty.SerializeToString,
            model__manager_dot_model__receiver__pb2.DownloadedModelResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CleanupModels(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.model_receiver.ModelReceiver/CleanupModels',
            model__manager_dot_model__receiver__pb2.CleanupModelsRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
