# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: model_manager/model_receiver.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'model_manager/model_receiver.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"model_manager/model_receiver.proto\x12\x15\x63\x61rbon.model_receiver\x1a\x0futil/util.proto\"C\n\x14\x44ownloadModelRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05model\x18\x04 \x01(\x0c\x12\x10\n\x08metadata\x18\x05 \x01(\x0c\"<\n\x05Model\x12\n\n\x02id\x18\x01 \x01(\t\x12\x11\n\tmodel_sha\x18\x02 \x01(\t\x12\x14\n\x0cmetadata_sha\x18\x03 \x01(\t\"G\n\x17\x44ownloadedModelResponse\x12,\n\x06models\x18\x01 \x03(\x0b\x32\x1c.carbon.model_receiver.Model\"(\n\x14\x43leanupModelsRequest\x12\x10\n\x08model_id\x18\x01 \x03(\t2\x8e\x02\n\rModelReceiver\x12P\n\rDownloadModel\x12+.carbon.model_receiver.DownloadModelRequest\x1a\x12.carbon.util.Empty\x12Y\n\x13GetDownloadedModels\x12\x12.carbon.util.Empty\x1a..carbon.model_receiver.DownloadedModelResponse\x12P\n\rCleanupModels\x12+.carbon.model_receiver.CleanupModelsRequest\x1a\x12.carbon.util.EmptyBHZFgithub.com/carbonrobotics/protos/golang/generated/proto/model_receiverb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'model_manager.model_receiver_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZFgithub.com/carbonrobotics/protos/golang/generated/proto/model_receiver'
  _globals['_DOWNLOADMODELREQUEST']._serialized_start=78
  _globals['_DOWNLOADMODELREQUEST']._serialized_end=145
  _globals['_MODEL']._serialized_start=147
  _globals['_MODEL']._serialized_end=207
  _globals['_DOWNLOADEDMODELRESPONSE']._serialized_start=209
  _globals['_DOWNLOADEDMODELRESPONSE']._serialized_end=280
  _globals['_CLEANUPMODELSREQUEST']._serialized_start=282
  _globals['_CLEANUPMODELSREQUEST']._serialized_end=322
  _globals['_MODELRECEIVER']._serialized_start=325
  _globals['_MODELRECEIVER']._serialized_end=595
# @@protoc_insertion_point(module_scope)
