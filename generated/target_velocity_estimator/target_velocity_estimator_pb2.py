# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: target_velocity_estimator/target_velocity_estimator.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'target_velocity_estimator/target_velocity_estimator.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9target_velocity_estimator/target_velocity_estimator.proto\x12\'carbon.aimbot.target_velocity_estimator\"G\n\rTVERowProfile\x12\x19\n\x11primary_kill_rate\x18\x01 \x01(\x02\x12\x1b\n\x13secondary_kill_rate\x18\x02 \x01(\x02\"\x90\x05\n\nTVEProfile\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\tupdate_ts\x18\x03 \x01(\x03\x12\x11\n\tprotected\x18\x04 \x01(\x08\x12\x1d\n\x15\x63ruise_offset_percent\x18\x05 \x01(\x02\x12\x15\n\rprimary_range\x18\x06 \x01(\x02\x12\x17\n\x0fsecondary_range\x18\x07 \x01(\x02\x12\x1a\n\x12increase_smoothing\x18\x08 \x01(\x02\x12\x1a\n\x12\x64\x65\x63rease_smoothing\x18\t \x01(\x02\x12I\n\x05row_1\x18\n \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfileB\x02\x18\x01\x12I\n\x05row_2\x18\x0b \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfileB\x02\x18\x01\x12I\n\x05row_3\x18\x0c \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfileB\x02\x18\x01\x12K\n\x04rows\x18\r \x03(\x0b\x32=.carbon.aimbot.target_velocity_estimator.TVEProfile.RowsEntry\x12\x13\n\x0bmin_vel_mph\x18\x0e \x01(\x02\x12\x13\n\x0bmax_vel_mph\x18\x0f \x01(\x02\x1a\x63\n\tRowsEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.carbon.aimbot.target_velocity_estimator.TVERowProfile:\x02\x38\x01\"*\n\x0eProfileDetails\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\tBSZQgithub.com/carbonrobotics/protos/golang/generated/proto/target_velocity_estimatorb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'target_velocity_estimator.target_velocity_estimator_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZQgithub.com/carbonrobotics/protos/golang/generated/proto/target_velocity_estimator'
  _globals['_TVEPROFILE_ROWSENTRY']._loaded_options = None
  _globals['_TVEPROFILE_ROWSENTRY']._serialized_options = b'8\001'
  _globals['_TVEPROFILE'].fields_by_name['row_1']._loaded_options = None
  _globals['_TVEPROFILE'].fields_by_name['row_1']._serialized_options = b'\030\001'
  _globals['_TVEPROFILE'].fields_by_name['row_2']._loaded_options = None
  _globals['_TVEPROFILE'].fields_by_name['row_2']._serialized_options = b'\030\001'
  _globals['_TVEPROFILE'].fields_by_name['row_3']._loaded_options = None
  _globals['_TVEPROFILE'].fields_by_name['row_3']._serialized_options = b'\030\001'
  _globals['_TVEROWPROFILE']._serialized_start=102
  _globals['_TVEROWPROFILE']._serialized_end=173
  _globals['_TVEPROFILE']._serialized_start=176
  _globals['_TVEPROFILE']._serialized_end=832
  _globals['_TVEPROFILE_ROWSENTRY']._serialized_start=733
  _globals['_TVEPROFILE_ROWSENTRY']._serialized_end=832
  _globals['_PROFILEDETAILS']._serialized_start=834
  _globals['_PROFILEDETAILS']._serialized_end=876
# @@protoc_insertion_point(module_scope)
