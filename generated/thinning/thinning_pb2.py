# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: thinning/thinning.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'thinning/thinning.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17thinning/thinning.proto\x12\x0f\x63\x61rbon.thinning\"$\n\x03\x42ox\x12\r\n\x05width\x18\x01 \x01(\x01\x12\x0e\n\x06height\x18\x02 \x01(\x01\"o\n\tDoubleBox\x12#\n\x05\x62ox_1\x18\x01 \x01(\x0b\x32\x14.carbon.thinning.Box\x12#\n\x05\x62ox_2\x18\x02 \x01(\x0b\x32\x14.carbon.thinning.Box\x12\x14\n\x0cideal_y_dist\x18\x03 \x01(\x01:\x02\x18\x01\"\x9d\x01\n\x13SizedNotSoGreedyCfg\x12)\n\x0bmin_keepout\x18\x01 \x01(\x0b\x32\x14.carbon.thinning.Box\x12\x1b\n\x13max_y_search_radius\x18\x02 \x01(\x01\x12\x14\n\x0cideal_y_dist\x18\x03 \x01(\x01\x12\x13\n\x0bsize_weight\x18\x04 \x01(\x01\x12\x13\n\x0b\x64ist_weight\x18\x05 \x01(\x01\"\xa8\x01\n\x06\x42ounds\x12#\n\x03\x62ox\x18\x01 \x01(\x0b\x32\x14.carbon.thinning.BoxH\x00\x12\x34\n\ndouble_box\x18\x02 \x01(\x0b\x32\x1a.carbon.thinning.DoubleBoxB\x02\x18\x01H\x00\x12\x39\n\tsized_cfg\x18\x03 \x01(\x0b\x32$.carbon.thinning.SizedNotSoGreedyCfgH\x00\x42\x08\n\x06\x62ounds\"T\n\nSizeFilter\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x14\n\x0csamples_size\x18\x02 \x01(\x04\x12\x1b\n\x13\x61\x63\x63\x65ptable_variance\x18\x03 \x01(\x01:\x02\x18\x01\"\xef\x02\n\x10\x43onfigDefinition\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\x06\x61\x63tive\x18\x02 \x01(\x08\x42\x02\x18\x01\x12&\n\x05row_1\x18\x03 \x01(\x0b\x32\x17.carbon.thinning.Bounds\x12&\n\x05row_2\x18\x04 \x01(\x0b\x32\x17.carbon.thinning.Bounds\x12&\n\x05row_3\x18\x05 \x01(\x0b\x32\x17.carbon.thinning.Bounds\x12\x34\n\x0bsize_filter\x18\x06 \x01(\x0b\x32\x1b.carbon.thinning.SizeFilterB\x02\x18\x01\x12\n\n\x02id\x18\x07 \x01(\t\x12\x39\n\x04rows\x18\x08 \x03(\x0b\x32+.carbon.thinning.ConfigDefinition.RowsEntry\x1a\x44\n\tRowsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.carbon.thinning.Bounds:\x02\x38\x01\x42\<EMAIL>/carbonrobotics/protos/golang/generated/proto/thinningb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'thinning.thinning_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/thinning'
  _globals['_DOUBLEBOX']._loaded_options = None
  _globals['_DOUBLEBOX']._serialized_options = b'\030\001'
  _globals['_BOUNDS'].fields_by_name['double_box']._loaded_options = None
  _globals['_BOUNDS'].fields_by_name['double_box']._serialized_options = b'\030\001'
  _globals['_SIZEFILTER']._loaded_options = None
  _globals['_SIZEFILTER']._serialized_options = b'\030\001'
  _globals['_CONFIGDEFINITION_ROWSENTRY']._loaded_options = None
  _globals['_CONFIGDEFINITION_ROWSENTRY']._serialized_options = b'8\001'
  _globals['_CONFIGDEFINITION'].fields_by_name['active']._loaded_options = None
  _globals['_CONFIGDEFINITION'].fields_by_name['active']._serialized_options = b'\030\001'
  _globals['_CONFIGDEFINITION'].fields_by_name['size_filter']._loaded_options = None
  _globals['_CONFIGDEFINITION'].fields_by_name['size_filter']._serialized_options = b'\030\001'
  _globals['_BOX']._serialized_start=44
  _globals['_BOX']._serialized_end=80
  _globals['_DOUBLEBOX']._serialized_start=82
  _globals['_DOUBLEBOX']._serialized_end=193
  _globals['_SIZEDNOTSOGREEDYCFG']._serialized_start=196
  _globals['_SIZEDNOTSOGREEDYCFG']._serialized_end=353
  _globals['_BOUNDS']._serialized_start=356
  _globals['_BOUNDS']._serialized_end=524
  _globals['_SIZEFILTER']._serialized_start=526
  _globals['_SIZEFILTER']._serialized_end=610
  _globals['_CONFIGDEFINITION']._serialized_start=613
  _globals['_CONFIGDEFINITION']._serialized_end=980
  _globals['_CONFIGDEFINITION_ROWSENTRY']._serialized_start=912
  _globals['_CONFIGDEFINITION_ROWSENTRY']._serialized_end=980
# @@protoc_insertion_point(module_scope)
