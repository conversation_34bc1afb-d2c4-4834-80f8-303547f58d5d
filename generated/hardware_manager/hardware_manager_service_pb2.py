# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: hardware_manager/hardware_manager_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'hardware_manager/hardware_manager_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/hardware_manager/hardware_manager_service.proto\x12\x10hardware_manager\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\r\"\x19\n\x0cPingResponse\x12\t\n\x01x\x18\x01 \x01(\r\"\x17\n\x15GetRotaryTicksRequest\"\x86\x02\n\x16GetRotaryTicksResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x18\n\x10\x66ront_left_ticks\x18\x02 \x01(\x03\x12\x19\n\x11\x66ront_right_ticks\x18\x03 \x01(\x03\x12\x17\n\x0f\x62\x61\x63k_left_ticks\x18\x04 \x01(\x03\x12\x18\n\x10\x62\x61\x63k_right_ticks\x18\x05 \x01(\x03\x12\x1a\n\x12\x66ront_left_enabled\x18\x06 \x01(\x08\x12\x1b\n\x13\x66ront_right_enabled\x18\x07 \x01(\x08\x12\x19\n\x11\x62\x61\x63k_left_enabled\x18\x08 \x01(\x08\x12\x1a\n\x12\x62\x61\x63k_right_enabled\x18\t \x01(\x08\".\n\x16GetNextDistanceRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\"A\n\x17GetNextDistanceResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x10\n\x08\x64istance\x18\x02 \x01(\x01\".\n\x16GetNextVelocityRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\"R\n\x17GetNextVelocityResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x04\x12\x11\n\tmm_per_ms\x18\x02 \x01(\x01\x12\x0e\n\x06lifted\x18\x03 \x01(\x08\"J\n\x15SetJimboxSpeedRequest\x12\x14\n\x0ctarget_speed\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63tual_ground_speed\x18\x02 \x01(\x01\"0\n\x16SetJimboxSpeedResponse\x12\x16\n\x0espeed_setpoint\x18\x01 \x01(\x01\"*\n\x17SetCruiseEnabledRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"+\n\x18SetCruiseEnabledResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x18\n\x16GetCruiseStatusRequest\"b\n\x17GetCruiseStatusResponse\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x11\n\tinstalled\x18\x02 \x01(\x08\x12\r\n\x05speed\x18\x03 \x01(\x01\x12\x14\n\x0c\x61llow_enable\x18\x04 \x01(\x08\"\x18\n\x16GetSafetyStatusRequest\"\xd9\x02\n\x17GetSafetyStatusResponse\x12\x0e\n\x06lifted\x18\x01 \x01(\x08\x12\x10\n\x08\x65stopped\x18\x02 \x01(\x08\x12\x17\n\x0fin_cab_estopped\x18\x03 \x01(\x08\x12\x15\n\rleft_estopped\x18\x04 \x01(\x08\x12\x16\n\x0eright_estopped\x18\x05 \x01(\x08\x12\x11\n\tlaser_key\x18\x06 \x01(\x08\x12\x11\n\tinterlock\x18\x07 \x01(\x08\x12\x15\n\rwater_protect\x18\x08 \x01(\x08\x12\x16\n\x0ereset_required\x18\t \x01(\x08\x12\x14\n\x0c\x63\x65nter_estop\x18\n \x01(\x08\x12\x1a\n\x12power_button_estop\x18\x0b \x01(\x08\x12\x1b\n\x13left_lpsu_interlock\x18\x0c \x01(\x08\x12\x1c\n\x14right_lpsu_interlock\x18\r \x01(\x08\x12\x12\n\ndebug_mode\x18\x0e \x01(\x08\" \n\x1eGetModemConnectionStateRequest\"Y\n\x1fGetModemConnectionStateResponse\x12\x1f\n\x17\x63onnection_strength_lte\x18\x01 \x01(\x02\x12\x15\n\rconnected_lte\x18\x02 \x01(\x08\"E\n\x06GeoLLA\x12\x0b\n\x03lat\x18\x01 \x01(\x01\x12\x0b\n\x03lng\x18\x02 \x01(\x01\x12\x0b\n\x03\x61lt\x18\x03 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x03\"@\n\x07GeoECEF\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\x12\x14\n\x0ctimestamp_ms\x18\x04 \x01(\x03\"\x13\n\x11GetGPSDataRequest\"d\n\x12GetGPSDataResponse\x12%\n\x03lla\x18\x01 \x01(\x0b\x32\x18.hardware_manager.GeoLLA\x12\'\n\x04\x65\x63\x65\x66\x18\x02 \x01(\x0b\x32\x19.hardware_manager.GeoECEF\"\x1e\n\x1cGetManagedBoardErrorsRequest\"e\n\x1dGetManagedBoardErrorsResponse\x12\r\n\x05\x62oard\x18\x01 \x03(\t\x12\x1a\n\x12\x65ncoder_error_flag\x18\x02 \x01(\x08\x12\x19\n\x11\x65ncoder_error_msg\x18\x03 \x01(\t\"\x1d\n\x1bGetSupervisoryStatusRequest\"\xb5\x0c\n\rChillerAlarms\x12\x19\n\x11low_level_in_tank\x18\x01 \x01(\x08\x12-\n%high_circulating_fluid_discharge_temp\x18\x02 \x01(\x08\x12-\n%circulating_fluid_discharge_temp_rise\x18\x03 \x01(\x08\x12-\n%circulating_fluid_discharge_temp_drop\x18\x04 \x01(\x08\x12*\n\"high_circulating_fluid_return_temp\x18\x05 \x01(\x08\x12\x31\n)circulating_fluid_discharge_pressure_rise\x18\x06 \x01(\x08\x12\x31\n)circulating_fluid_discharge_pressure_drop\x18\x07 \x01(\x08\x12$\n\x1chigh_compressor_suction_temp\x18\x08 \x01(\x08\x12#\n\x1blow_compressor_suction_temp\x18\t \x01(\x08\x12\x1b\n\x13low_super_heat_temp\x18\n \x01(\x08\x12*\n\"high_compressor_discharge_pressure\x18\x0b \x01(\x08\x12-\n%refrigerant_circut_pressure_high_drop\x18\x0c \x01(\x08\x12,\n$refrigerant_circut_pressure_low_rise\x18\r \x01(\x08\x12,\n$refrigerant_circut_pressure_low_drop\x18\x0e \x01(\x08\x12\"\n\x1a\x63ompressor_running_failure\x18\x0f \x01(\x08\x12\x1b\n\x13\x63ommunication_error\x18\x10 \x01(\x08\x12\x14\n\x0cmemory_error\x18\x11 \x01(\x08\x12\x18\n\x10\x64\x63_line_fuse_cut\x18\x12 \x01(\x08\x12\x37\n/circulating_fluid_discharge_temp_sensor_failure\x18\x13 \x01(\x08\x12\x34\n,circulating_fluid_return_temp_sensor_failure\x18\x14 \x01(\x08\x12\x35\n-circulating_fluid_suction_temp_sensor_failure\x18\x15 \x01(\x08\x12;\n3circulating_fluid_discharge_pressure_sensor_failure\x18\x16 \x01(\x08\x12\x34\n,compressor_discharge_pressure_sensor_failure\x18\x17 \x01(\x08\x12\x32\n*compressor_suction_pressure_sensor_failure\x18\x18 \x01(\x08\x12\x18\n\x10pump_maintenance\x18\x19 \x01(\x08\x12\x17\n\x0f\x66\x61n_maintenance\x18\x1a \x01(\x08\x12\x1e\n\x16\x63ompressor_maintenance\x18\x1b \x01(\x08\x12(\n contact_input_1_signal_detection\x18\x1c \x01(\x08\x12(\n contact_input_2_signal_detection\x18\x1d \x01(\x08\x12\x30\n(compressor_discharge_temp_sensor_failure\x18\x1e \x01(\x08\x12&\n\x1e\x63ompressor_discharge_temp_rise\x18\x1f \x01(\x08\x12$\n\x1c\x64ustproof_filter_maintenance\x18  \x01(\x08\x12\x16\n\x0epower_stoppage\x18! \x01(\x08\x12\x1a\n\x12\x63ompressor_waiting\x18\" \x01(\x08\x12\x13\n\x0b\x66\x61n_failure\x18# \x01(\x08\x12\x1f\n\x17\x63ompressor_over_current\x18$ \x01(\x08\x12\x19\n\x11pump_over_current\x18% \x01(\x08\x12 \n\x18\x61ir_exhaust_fan_stoppage\x18& \x01(\x08\x12\x1d\n\x15incorrect_phase_error\x18\' \x01(\x08\x12 \n\x18phase_board_over_current\x18( \x01(\x08\"\xed\x08\n\x1cGetSupervisoryStatusResponse\x12\x1c\n\x14water_protect_status\x18\x01 \x01(\x08\x12 \n\x18main_contactor_status_fb\x18\x02 \x01(\x08\x12\x12\n\npower_good\x18\x03 \x01(\x08\x12\x11\n\tpower_bad\x18\x04 \x01(\x08\x12\x16\n\x0epower_very_bad\x18\x05 \x01(\x08\x12\x15\n\rlifted_status\x18\x06 \x01(\x08\x12\x1c\n\x14temp_humidity_status\x18\x07 \x01(\x08\x12\x15\n\rtractor_power\x18\x08 \x01(\x08\x12\x14\n\x0c\x61\x63_frequency\x18\t \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_a_b\x18\n \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_b_c\x18\x0b \x01(\x01\x12\x16\n\x0e\x61\x63_voltage_a_c\x18\x0c \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_a\x18\r \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_b\x18\x0e \x01(\x01\x12\x14\n\x0c\x61\x63_voltage_c\x18\x0f \x01(\x01\x12\x17\n\x0fphase_power_w_3\x18\x10 \x01(\x03\x12\x18\n\x10phase_power_va_3\x18\x11 \x01(\x03\x12\x14\n\x0cpower_factor\x18\x12 \x01(\x01\x12\x1b\n\x13server_cabinet_temp\x18\x13 \x01(\x01\x12\x1f\n\x17server_cabinet_humidity\x18\x14 \x01(\x01\x12\x1b\n\x13\x62\x61ttery_voltage_12v\x18\x15 \x01(\x01\x12\'\n\x1btemp_humidity_bypass_status\x18\x16 \x01(\x08\x42\x02\x18\x01\x12\x1a\n\x12temp_bypass_status\x18\x17 \x01(\x08\x12\x1e\n\x16humidity_bypass_status\x18\x18 \x01(\x08\x12\x13\n\x0btemp_status\x18\x19 \x01(\x08\x12\x17\n\x0fhumidity_status\x18\x1a \x01(\x08\x12\x14\n\x0c\x62tl_disabled\x18\x1b \x03(\x08\x12\x17\n\x0fserver_disabled\x18\x1c \x03(\x08\x12\x19\n\x11scanners_disabled\x18\x1d \x03(\x08\x12\x1e\n\x16wheel_encoder_disabled\x18\x1e \x01(\x08\x12\x17\n\x0fstrobe_disabled\x18\x1f \x01(\x08\x12\x14\n\x0cgps_disabled\x18  \x01(\x08\x12\x1f\n\x17main_contactor_disabled\x18! \x01(\x08\x12 \n\x18\x61ir_conditioner_disabled\x18\" \x01(\x08\x12\x18\n\x10\x63hiller_disabled\x18# \x01(\x08\x12\x14\n\x0c\x63hiller_temp\x18$ \x01(\x01\x12\x14\n\x0c\x63hiller_flow\x18% \x01(\x01\x12\x18\n\x10\x63hiller_pressure\x18& \x01(\x01\x12\x1c\n\x14\x63hiller_conductivity\x18\' \x01(\x01\x12\x18\n\x10\x63hiller_set_temp\x18( \x01(\x01\x12\x37\n\x0e\x63hiller_alarms\x18) \x01(\x0b\x32\x1f.hardware_manager.ChillerAlarms\":\n\x17SetServerDisableRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x0f\n\x07\x64isable\x18\x02 \x01(\x08\"+\n\x18SetServerDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"7\n\x14SetBTLDisableRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x0f\n\x07\x64isable\x18\x02 \x01(\x08\"(\n\x15SetBTLDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"<\n\x19SetScannersDisableRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x03\x12\x0f\n\x07\x64isable\x18\x02 \x01(\x08\"-\n\x1aSetScannersDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"5\n\"SetWheelEncoderBoardDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"6\n#SetWheelEncoderBoardDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"M\n\x1dSetWheelEncoderDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\x12\r\n\x05\x66ront\x18\x02 \x01(\x08\x12\x0c\n\x04left\x18\x03 \x01(\x08\"1\n\x1eSetWheelEncoderDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"*\n\x17SetStrobeDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"+\n\x18SetStrobeDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\'\n\x14SetGPSDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"(\n\x15SetGPSDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\"\n CommandComputerPowerCycleRequest\"4\n!CommandComputerPowerCycleResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x16\n\x14SuicideSwitchRequest\"(\n\x15SuicideSwitchResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"1\n\x1eSetMainContactorDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"2\n\x1fSetMainContactorDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"2\n\x1fSetAirConditionerDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"3\n SetAirConditionerDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"+\n\x18SetChillerDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\",\n\x19SetChillerDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\".\n\x1bSetTempBypassDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"/\n\x1cSetTempBypassDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"2\n\x1fSetHumidityBypassDisableRequest\x12\x0f\n\x07\x64isable\x18\x01 \x01(\x08\"3\n SetHumidityBypassDisableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x1f\n\x1dGetAvailableUSBStorageRequest\"V\n\x1eGetAvailableUSBStorageResponse\x12\x0c\n\x04used\x18\x01 \x01(\x02\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rusb_available\x18\x03 \x01(\x08\"\x11\n\x0fGetReadyRequest\"!\n\x10GetReadyResponse\x12\r\n\x05ready\x18\x01 \x01(\x08\"\x16\n\x14Get240vUptimeRequest\")\n\x15Get240vUptimeResponse\x12\x10\n\x08uptime_s\x18\x01 \x01(\x03\"%\n\x17GetDeltaTravelMMRequest\x12\n\n\x02id\x18\x01 \x01(\t\",\n\x18GetDeltaTravelMMResponse\x12\x10\n\x08\x64\x65lta_mm\x18\x01 \x01(\x01\"\x13\n\x11GetRuntimeRequest\"*\n\x12GetRuntimeResponse\x12\x14\n\x0cruntime_240v\x18\x01 \x01(\r\"\"\n GetWheelEncoderResolutionRequest\"7\n!GetWheelEncoderResolutionResponse\x12\x12\n\nresolution\x18\x01 \x01(\r\"\xa6\x01\n\x0eStrobeSettings\x12\x18\n\x0b\x65xposure_us\x18\x01 \x01(\rH\x00\x88\x01\x01\x12\x16\n\tperiod_us\x18\x02 \x01(\rH\x01\x88\x01\x01\x12&\n\x19targets_per_predict_ratio\x18\x03 \x01(\rH\x02\x88\x01\x01\x42\x0e\n\x0c_exposure_usB\x0c\n\n_period_usB\x1c\n\x1a_targets_per_predict_ratio\",\n\x19SetStrobeSettingsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x1a\n\x18GetStrobeSettingsRequest2\xf3\x1d\n\x16HardwareManagerService\x12G\n\x04Ping\x12\x1d.hardware_manager.PingRequest\x1a\x1e.hardware_manager.PingResponse\"\x00\x12S\n\x08GetReady\x12!.hardware_manager.GetReadyRequest\x1a\".hardware_manager.GetReadyResponse\"\x00\x12h\n\x0fGetNextDistance\x12(.hardware_manager.GetNextDistanceRequest\x1a).hardware_manager.GetNextDistanceResponse\"\x00\x12h\n\x0fGetNextVelocity\x12(.hardware_manager.GetNextVelocityRequest\x1a).hardware_manager.GetNextVelocityResponse\"\x00\x12\x65\n\x0eGetRotaryTicks\x12\'.hardware_manager.GetRotaryTicksRequest\x1a(.hardware_manager.GetRotaryTicksResponse\"\x00\x12k\n\x10GetDeltaTravelMM\x12).hardware_manager.GetDeltaTravelMMRequest\x1a*.hardware_manager.GetDeltaTravelMMResponse\"\x00\x12\x86\x01\n\x19GetWheelEncoderResolution\x12\x32.hardware_manager.GetWheelEncoderResolutionRequest\x1a\x33.hardware_manager.GetWheelEncoderResolutionResponse\"\x00\x12h\n\x0fGetSafetyStatus\x12(.hardware_manager.GetSafetyStatusRequest\x1a).hardware_manager.GetSafetyStatusResponse\"\x00\x12\x80\x01\n\x17GetModemConnectionState\x12\x30.hardware_manager.GetModemConnectionStateRequest\x1a\x31.hardware_manager.GetModemConnectionStateResponse\"\x00\x12Y\n\nGetGPSData\x12#.hardware_manager.GetGPSDataRequest\x1a$.hardware_manager.GetGPSDataResponse\"\x00\x12\x64\n\x11SetStrobeSettings\x12 .hardware_manager.StrobeSettings\x1a+.hardware_manager.SetStrobeSettingsResponse\"\x00\x12\x63\n\x11GetStrobeSettings\x12*.hardware_manager.GetStrobeSettingsRequest\x1a .hardware_manager.StrobeSettings\"\x00\x12z\n\x15GetManagedBoardErrors\x12..hardware_manager.GetManagedBoardErrorsRequest\x1a/.hardware_manager.GetManagedBoardErrorsResponse\"\x00\x12w\n\x14GetSupervisoryStatus\x12-.hardware_manager.GetSupervisoryStatusRequest\x1a..hardware_manager.GetSupervisoryStatusResponse\"\x00\x12k\n\x10SetServerDisable\x12).hardware_manager.SetServerDisableRequest\x1a*.hardware_manager.SetServerDisableResponse\"\x00\x12\x62\n\rSetBTLDisable\x12&.hardware_manager.SetBTLDisableRequest\x1a\'.hardware_manager.SetBTLDisableResponse\"\x00\x12q\n\x12SetScannersDisable\x12+.hardware_manager.SetScannersDisableRequest\x1a,.hardware_manager.SetScannersDisableResponse\"\x00\x12\x8c\x01\n\x1bSetWheelEncoderBoardDisable\x12\x34.hardware_manager.SetWheelEncoderBoardDisableRequest\x1a\x35.hardware_manager.SetWheelEncoderBoardDisableResponse\"\x00\x12}\n\x16SetWheelEncoderDisable\x12/.hardware_manager.SetWheelEncoderDisableRequest\x1a\x30.hardware_manager.SetWheelEncoderDisableResponse\"\x00\x12\x62\n\rSetGPSDisable\x12&.hardware_manager.SetGPSDisableRequest\x1a\'.hardware_manager.SetGPSDisableResponse\"\x00\x12\x62\n\rSuicideSwitch\x12&.hardware_manager.SuicideSwitchRequest\x1a\'.hardware_manager.SuicideSwitchResponse\"\x00\x12\x86\x01\n\x19\x43ommandComputerPowerCycle\x12\x32.hardware_manager.CommandComputerPowerCycleRequest\x1a\x33.hardware_manager.CommandComputerPowerCycleResponse\"\x00\x12\x80\x01\n\x17SetMainContactorDisable\x12\x30.hardware_manager.SetMainContactorDisableRequest\x1a\x31.hardware_manager.SetMainContactorDisableResponse\"\x00\x12k\n\x10SetStrobeDisable\x12).hardware_manager.SetStrobeDisableRequest\x1a*.hardware_manager.SetStrobeDisableResponse\"\x00\x12\x83\x01\n\x18SetAirConditionerDisable\x12\x31.hardware_manager.SetAirConditionerDisableRequest\x1a\x32.hardware_manager.SetAirConditionerDisableResponse\"\x00\x12n\n\x11SetChillerDisable\x12*.hardware_manager.SetChillerDisableRequest\x1a+.hardware_manager.SetChillerDisableResponse\"\x00\x12w\n\x14SetTempBypassDisable\x12-.hardware_manager.SetTempBypassDisableRequest\x1a..hardware_manager.SetTempBypassDisableResponse\"\x00\x12\x83\x01\n\x18SetHumidityBypassDisable\x12\x31.hardware_manager.SetHumidityBypassDisableRequest\x1a\x32.hardware_manager.SetHumidityBypassDisableResponse\"\x00\x12\x62\n\rGet240vUptime\x12&.hardware_manager.Get240vUptimeRequest\x1a\'.hardware_manager.Get240vUptimeResponse\"\x00\x12Y\n\nGetRuntime\x12#.hardware_manager.GetRuntimeRequest\x1a$.hardware_manager.GetRuntimeResponse\"\x00\x12}\n\x16GetAvailableUSBStorage\x12/.hardware_manager.GetAvailableUSBStorageRequest\x1a\x30.hardware_manager.GetAvailableUSBStorageResponse\"\x00\x12\x65\n\x0eSetJimboxSpeed\x12\'.hardware_manager.SetJimboxSpeedRequest\x1a(.hardware_manager.SetJimboxSpeedResponse\"\x00\x12k\n\x10SetCruiseEnabled\x12).hardware_manager.SetCruiseEnabledRequest\x1a*.hardware_manager.SetCruiseEnabledResponse\"\x00\x12h\n\x0fGetCruiseStatus\x12(.hardware_manager.GetCruiseStatusRequest\x1a).hardware_manager.GetCruiseStatusResponse\"\x00\x42JZHgithub.com/carbonrobotics/protos/golang/generated/proto/hardware_managerb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'hardware_manager.hardware_manager_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZHgithub.com/carbonrobotics/protos/golang/generated/proto/hardware_manager'
  _globals['_GETSUPERVISORYSTATUSRESPONSE'].fields_by_name['temp_humidity_bypass_status']._loaded_options = None
  _globals['_GETSUPERVISORYSTATUSRESPONSE'].fields_by_name['temp_humidity_bypass_status']._serialized_options = b'\030\001'
  _globals['_PINGREQUEST']._serialized_start=69
  _globals['_PINGREQUEST']._serialized_end=93
  _globals['_PINGRESPONSE']._serialized_start=95
  _globals['_PINGRESPONSE']._serialized_end=120
  _globals['_GETROTARYTICKSREQUEST']._serialized_start=122
  _globals['_GETROTARYTICKSREQUEST']._serialized_end=145
  _globals['_GETROTARYTICKSRESPONSE']._serialized_start=148
  _globals['_GETROTARYTICKSRESPONSE']._serialized_end=410
  _globals['_GETNEXTDISTANCEREQUEST']._serialized_start=412
  _globals['_GETNEXTDISTANCEREQUEST']._serialized_end=458
  _globals['_GETNEXTDISTANCERESPONSE']._serialized_start=460
  _globals['_GETNEXTDISTANCERESPONSE']._serialized_end=525
  _globals['_GETNEXTVELOCITYREQUEST']._serialized_start=527
  _globals['_GETNEXTVELOCITYREQUEST']._serialized_end=573
  _globals['_GETNEXTVELOCITYRESPONSE']._serialized_start=575
  _globals['_GETNEXTVELOCITYRESPONSE']._serialized_end=657
  _globals['_SETJIMBOXSPEEDREQUEST']._serialized_start=659
  _globals['_SETJIMBOXSPEEDREQUEST']._serialized_end=733
  _globals['_SETJIMBOXSPEEDRESPONSE']._serialized_start=735
  _globals['_SETJIMBOXSPEEDRESPONSE']._serialized_end=783
  _globals['_SETCRUISEENABLEDREQUEST']._serialized_start=785
  _globals['_SETCRUISEENABLEDREQUEST']._serialized_end=827
  _globals['_SETCRUISEENABLEDRESPONSE']._serialized_start=829
  _globals['_SETCRUISEENABLEDRESPONSE']._serialized_end=872
  _globals['_GETCRUISESTATUSREQUEST']._serialized_start=874
  _globals['_GETCRUISESTATUSREQUEST']._serialized_end=898
  _globals['_GETCRUISESTATUSRESPONSE']._serialized_start=900
  _globals['_GETCRUISESTATUSRESPONSE']._serialized_end=998
  _globals['_GETSAFETYSTATUSREQUEST']._serialized_start=1000
  _globals['_GETSAFETYSTATUSREQUEST']._serialized_end=1024
  _globals['_GETSAFETYSTATUSRESPONSE']._serialized_start=1027
  _globals['_GETSAFETYSTATUSRESPONSE']._serialized_end=1372
  _globals['_GETMODEMCONNECTIONSTATEREQUEST']._serialized_start=1374
  _globals['_GETMODEMCONNECTIONSTATEREQUEST']._serialized_end=1406
  _globals['_GETMODEMCONNECTIONSTATERESPONSE']._serialized_start=1408
  _globals['_GETMODEMCONNECTIONSTATERESPONSE']._serialized_end=1497
  _globals['_GEOLLA']._serialized_start=1499
  _globals['_GEOLLA']._serialized_end=1568
  _globals['_GEOECEF']._serialized_start=1570
  _globals['_GEOECEF']._serialized_end=1634
  _globals['_GETGPSDATAREQUEST']._serialized_start=1636
  _globals['_GETGPSDATAREQUEST']._serialized_end=1655
  _globals['_GETGPSDATARESPONSE']._serialized_start=1657
  _globals['_GETGPSDATARESPONSE']._serialized_end=1757
  _globals['_GETMANAGEDBOARDERRORSREQUEST']._serialized_start=1759
  _globals['_GETMANAGEDBOARDERRORSREQUEST']._serialized_end=1789
  _globals['_GETMANAGEDBOARDERRORSRESPONSE']._serialized_start=1791
  _globals['_GETMANAGEDBOARDERRORSRESPONSE']._serialized_end=1892
  _globals['_GETSUPERVISORYSTATUSREQUEST']._serialized_start=1894
  _globals['_GETSUPERVISORYSTATUSREQUEST']._serialized_end=1923
  _globals['_CHILLERALARMS']._serialized_start=1926
  _globals['_CHILLERALARMS']._serialized_end=3515
  _globals['_GETSUPERVISORYSTATUSRESPONSE']._serialized_start=3518
  _globals['_GETSUPERVISORYSTATUSRESPONSE']._serialized_end=4651
  _globals['_SETSERVERDISABLEREQUEST']._serialized_start=4653
  _globals['_SETSERVERDISABLEREQUEST']._serialized_end=4711
  _globals['_SETSERVERDISABLERESPONSE']._serialized_start=4713
  _globals['_SETSERVERDISABLERESPONSE']._serialized_end=4756
  _globals['_SETBTLDISABLEREQUEST']._serialized_start=4758
  _globals['_SETBTLDISABLEREQUEST']._serialized_end=4813
  _globals['_SETBTLDISABLERESPONSE']._serialized_start=4815
  _globals['_SETBTLDISABLERESPONSE']._serialized_end=4855
  _globals['_SETSCANNERSDISABLEREQUEST']._serialized_start=4857
  _globals['_SETSCANNERSDISABLEREQUEST']._serialized_end=4917
  _globals['_SETSCANNERSDISABLERESPONSE']._serialized_start=4919
  _globals['_SETSCANNERSDISABLERESPONSE']._serialized_end=4964
  _globals['_SETWHEELENCODERBOARDDISABLEREQUEST']._serialized_start=4966
  _globals['_SETWHEELENCODERBOARDDISABLEREQUEST']._serialized_end=5019
  _globals['_SETWHEELENCODERBOARDDISABLERESPONSE']._serialized_start=5021
  _globals['_SETWHEELENCODERBOARDDISABLERESPONSE']._serialized_end=5075
  _globals['_SETWHEELENCODERDISABLEREQUEST']._serialized_start=5077
  _globals['_SETWHEELENCODERDISABLEREQUEST']._serialized_end=5154
  _globals['_SETWHEELENCODERDISABLERESPONSE']._serialized_start=5156
  _globals['_SETWHEELENCODERDISABLERESPONSE']._serialized_end=5205
  _globals['_SETSTROBEDISABLEREQUEST']._serialized_start=5207
  _globals['_SETSTROBEDISABLEREQUEST']._serialized_end=5249
  _globals['_SETSTROBEDISABLERESPONSE']._serialized_start=5251
  _globals['_SETSTROBEDISABLERESPONSE']._serialized_end=5294
  _globals['_SETGPSDISABLEREQUEST']._serialized_start=5296
  _globals['_SETGPSDISABLEREQUEST']._serialized_end=5335
  _globals['_SETGPSDISABLERESPONSE']._serialized_start=5337
  _globals['_SETGPSDISABLERESPONSE']._serialized_end=5377
  _globals['_COMMANDCOMPUTERPOWERCYCLEREQUEST']._serialized_start=5379
  _globals['_COMMANDCOMPUTERPOWERCYCLEREQUEST']._serialized_end=5413
  _globals['_COMMANDCOMPUTERPOWERCYCLERESPONSE']._serialized_start=5415
  _globals['_COMMANDCOMPUTERPOWERCYCLERESPONSE']._serialized_end=5467
  _globals['_SUICIDESWITCHREQUEST']._serialized_start=5469
  _globals['_SUICIDESWITCHREQUEST']._serialized_end=5491
  _globals['_SUICIDESWITCHRESPONSE']._serialized_start=5493
  _globals['_SUICIDESWITCHRESPONSE']._serialized_end=5533
  _globals['_SETMAINCONTACTORDISABLEREQUEST']._serialized_start=5535
  _globals['_SETMAINCONTACTORDISABLEREQUEST']._serialized_end=5584
  _globals['_SETMAINCONTACTORDISABLERESPONSE']._serialized_start=5586
  _globals['_SETMAINCONTACTORDISABLERESPONSE']._serialized_end=5636
  _globals['_SETAIRCONDITIONERDISABLEREQUEST']._serialized_start=5638
  _globals['_SETAIRCONDITIONERDISABLEREQUEST']._serialized_end=5688
  _globals['_SETAIRCONDITIONERDISABLERESPONSE']._serialized_start=5690
  _globals['_SETAIRCONDITIONERDISABLERESPONSE']._serialized_end=5741
  _globals['_SETCHILLERDISABLEREQUEST']._serialized_start=5743
  _globals['_SETCHILLERDISABLEREQUEST']._serialized_end=5786
  _globals['_SETCHILLERDISABLERESPONSE']._serialized_start=5788
  _globals['_SETCHILLERDISABLERESPONSE']._serialized_end=5832
  _globals['_SETTEMPBYPASSDISABLEREQUEST']._serialized_start=5834
  _globals['_SETTEMPBYPASSDISABLEREQUEST']._serialized_end=5880
  _globals['_SETTEMPBYPASSDISABLERESPONSE']._serialized_start=5882
  _globals['_SETTEMPBYPASSDISABLERESPONSE']._serialized_end=5929
  _globals['_SETHUMIDITYBYPASSDISABLEREQUEST']._serialized_start=5931
  _globals['_SETHUMIDITYBYPASSDISABLEREQUEST']._serialized_end=5981
  _globals['_SETHUMIDITYBYPASSDISABLERESPONSE']._serialized_start=5983
  _globals['_SETHUMIDITYBYPASSDISABLERESPONSE']._serialized_end=6034
  _globals['_GETAVAILABLEUSBSTORAGEREQUEST']._serialized_start=6036
  _globals['_GETAVAILABLEUSBSTORAGEREQUEST']._serialized_end=6067
  _globals['_GETAVAILABLEUSBSTORAGERESPONSE']._serialized_start=6069
  _globals['_GETAVAILABLEUSBSTORAGERESPONSE']._serialized_end=6155
  _globals['_GETREADYREQUEST']._serialized_start=6157
  _globals['_GETREADYREQUEST']._serialized_end=6174
  _globals['_GETREADYRESPONSE']._serialized_start=6176
  _globals['_GETREADYRESPONSE']._serialized_end=6209
  _globals['_GET240VUPTIMEREQUEST']._serialized_start=6211
  _globals['_GET240VUPTIMEREQUEST']._serialized_end=6233
  _globals['_GET240VUPTIMERESPONSE']._serialized_start=6235
  _globals['_GET240VUPTIMERESPONSE']._serialized_end=6276
  _globals['_GETDELTATRAVELMMREQUEST']._serialized_start=6278
  _globals['_GETDELTATRAVELMMREQUEST']._serialized_end=6315
  _globals['_GETDELTATRAVELMMRESPONSE']._serialized_start=6317
  _globals['_GETDELTATRAVELMMRESPONSE']._serialized_end=6361
  _globals['_GETRUNTIMEREQUEST']._serialized_start=6363
  _globals['_GETRUNTIMEREQUEST']._serialized_end=6382
  _globals['_GETRUNTIMERESPONSE']._serialized_start=6384
  _globals['_GETRUNTIMERESPONSE']._serialized_end=6426
  _globals['_GETWHEELENCODERRESOLUTIONREQUEST']._serialized_start=6428
  _globals['_GETWHEELENCODERRESOLUTIONREQUEST']._serialized_end=6462
  _globals['_GETWHEELENCODERRESOLUTIONRESPONSE']._serialized_start=6464
  _globals['_GETWHEELENCODERRESOLUTIONRESPONSE']._serialized_end=6519
  _globals['_STROBESETTINGS']._serialized_start=6522
  _globals['_STROBESETTINGS']._serialized_end=6688
  _globals['_SETSTROBESETTINGSRESPONSE']._serialized_start=6690
  _globals['_SETSTROBESETTINGSRESPONSE']._serialized_end=6734
  _globals['_GETSTROBESETTINGSREQUEST']._serialized_start=6736
  _globals['_GETSTROBESETTINGSREQUEST']._serialized_end=6762
  _globals['_HARDWAREMANAGERSERVICE']._serialized_start=6765
  _globals['_HARDWAREMANAGERSERVICE']._serialized_end=10592
# @@protoc_insertion_point(module_scope)
