# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: frontend/almanac.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'frontend/almanac.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.almanac import almanac_pb2 as almanac_dot_almanac__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16\x66rontend/almanac.proto\x12\x17\x63\x61rbon.frontend.almanac\x1a\x0futil/util.proto\x1a\x15\x61lmanac/almanac.proto\"\xf4\x02\n\x15GetConfigDataResponse\x12\x1b\n\x13num_size_categories\x18\x01 \x01(\r\x12\x62\n\x13\x63rop_category_names\x18\x02 \x03(\x0b\x32\x45.carbon.frontend.almanac.GetConfigDataResponse.CropCategoryNamesEntry\x12\x62\n\x13weed_category_names\x18\x03 \x03(\x0b\x32\x45.carbon.frontend.almanac.GetConfigDataResponse.WeedCategoryNamesEntry\x1a\x38\n\x16\x43ropCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x38\n\x16WeedCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01:\x02\x18\x01\"&\n\x18LoadAlmanacConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"Q\n\x19LoadAlmanacConfigResponse\x12\x34\n\x06\x63onfig\x18\x01 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfig\"d\n\x18SaveAlmanacConfigRequest\x12\x34\n\x06\x63onfig\x18\x01 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfig\x12\x12\n\nset_active\x18\x02 \x01(\x08\"\'\n\x19SaveAlmanacConfigResponse\x12\n\n\x02id\x18\x01 \x01(\t\"+\n\x1dSetActiveAlmanacConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"?\n\x1a\x44\x65leteAlmanacConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x15\n\rnew_active_id\x18\x02 \x01(\t\"\xdd\x01\n\x1cGetNextAlmanacConfigResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\t\x12W\n\tavailable\x18\x03 \x03(\x0b\x32\x44.carbon.frontend.almanac.GetNextAlmanacConfigResponse.AvailableEntry\x1a\x30\n\x0e\x41vailableEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\",\n\x1eLoadDiscriminatorConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"]\n\x1fLoadDiscriminatorConfigResponse\x12:\n\x06\x63onfig\x18\x01 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfig\"\x80\x01\n\x1eSaveDiscriminatorConfigRequest\x12:\n\x06\x63onfig\x18\x01 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfig\x12\"\n\x1a\x61ssociate_with_active_crop\x18\x02 \x01(\x08\"-\n\x1fSaveDiscriminatorConfigResponse\x12\n\n\x02id\x18\x01 \x01(\t\"S\n#SetActiveDiscriminatorConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x07\x63rop_id\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\n\n\x08_crop_id\".\n DeleteDiscriminatorConfigRequest\x12\n\n\x02id\x18\x01 \x01(\t\"\xe9\x01\n\"GetNextDiscriminatorConfigResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\t\x12]\n\tavailable\x18\x03 \x03(\x0b\x32J.carbon.frontend.almanac.GetNextDiscriminatorConfigResponse.AvailableEntry\x1a\x30\n\x0e\x41vailableEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x80\x01\n GetNextModelinatorConfigResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x38\n\x06\x63onfig\x18\x02 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\"X\n\x1cSaveModelinatorConfigRequest\x12\x38\n\x06\x63onfig\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\"B\n\x1d\x46\x65tchModelinatorConfigRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\"Z\n\x1e\x46\x65tchModelinatorConfigResponse\x12\x38\n\x06\x63onfig\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\"\x1f\n\x1dResetModelinatorConfigRequest\"L\n\x18GetNextConfigDataRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0c\n\x04lang\x18\x02 \x01(\t\"\xa0\x03\n\x19GetNextConfigDataResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x1b\n\x13num_size_categories\x18\x02 \x01(\r\x12\x66\n\x13\x63rop_category_names\x18\x03 \x03(\x0b\x32I.carbon.frontend.almanac.GetNextConfigDataResponse.CropCategoryNamesEntry\x12\x66\n\x13weed_category_names\x18\x04 \x03(\x0b\x32I.carbon.frontend.almanac.GetNextConfigDataResponse.WeedCategoryNamesEntry\x1a\x38\n\x16\x43ropCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x38\n\x16WeedCategoryNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x32\xc5\x0e\n\x14\x41lmanacConfigService\x12X\n\rGetConfigData\x12\x12.carbon.util.Empty\x1a..carbon.frontend.almanac.GetConfigDataResponse\"\x03\x88\x02\x01\x12z\n\x11GetNextConfigData\x12\x31.carbon.frontend.almanac.GetNextConfigDataRequest\x1a\x32.carbon.frontend.almanac.GetNextConfigDataResponse\x12z\n\x11LoadAlmanacConfig\x12\x31.carbon.frontend.almanac.LoadAlmanacConfigRequest\x1a\x32.carbon.frontend.almanac.LoadAlmanacConfigResponse\x12z\n\x11SaveAlmanacConfig\x12\x31.carbon.frontend.almanac.SaveAlmanacConfigRequest\x1a\x32.carbon.frontend.almanac.SaveAlmanacConfigResponse\x12\x64\n\x16SetActiveAlmanacConfig\x12\x36.carbon.frontend.almanac.SetActiveAlmanacConfigRequest\x1a\x12.carbon.util.Empty\x12^\n\x13\x44\x65leteAlmanacConfig\x12\x33.carbon.frontend.almanac.DeleteAlmanacConfigRequest\x1a\x12.carbon.util.Empty\x12\x65\n\x14GetNextAlmanacConfig\x12\x16.carbon.util.Timestamp\x1a\x35.carbon.frontend.almanac.GetNextAlmanacConfigResponse\x12\x8c\x01\n\x17LoadDiscriminatorConfig\x12\x37.carbon.frontend.almanac.LoadDiscriminatorConfigRequest\x1a\x38.carbon.frontend.almanac.LoadDiscriminatorConfigResponse\x12\x8c\x01\n\x17SaveDiscriminatorConfig\x12\x37.carbon.frontend.almanac.SaveDiscriminatorConfigRequest\x1a\x38.carbon.frontend.almanac.SaveDiscriminatorConfigResponse\x12p\n\x1cSetActiveDiscriminatorConfig\x12<.carbon.frontend.almanac.SetActiveDiscriminatorConfigRequest\x1a\x12.carbon.util.Empty\x12j\n\x19\x44\x65leteDiscriminatorConfig\x12\x39.carbon.frontend.almanac.DeleteDiscriminatorConfigRequest\x1a\x12.carbon.util.Empty\x12q\n\x1aGetNextDiscriminatorConfig\x12\x16.carbon.util.Timestamp\x1a;.carbon.frontend.almanac.GetNextDiscriminatorConfigResponse\x12m\n\x18GetNextModelinatorConfig\x12\x16.carbon.util.Timestamp\x1a\x39.carbon.frontend.almanac.GetNextModelinatorConfigResponse\x12\x62\n\x15SaveModelinatorConfig\x12\x35.carbon.frontend.almanac.SaveModelinatorConfigRequest\x1a\x12.carbon.util.Empty\x12\x89\x01\n\x16\x46\x65tchModelinatorConfig\x12\x36.carbon.frontend.almanac.FetchModelinatorConfigRequest\x1a\x37.carbon.frontend.almanac.FetchModelinatorConfigResponse\x12\x64\n\x16ResetModelinatorConfig\x12\x36.carbon.frontend.almanac.ResetModelinatorConfigRequest\x1a\<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontendb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'frontend.almanac_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/frontend'
  _globals['_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._loaded_options = None
  _globals['_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._serialized_options = b'8\001'
  _globals['_GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._loaded_options = None
  _globals['_GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._serialized_options = b'8\001'
  _globals['_GETCONFIGDATARESPONSE']._loaded_options = None
  _globals['_GETCONFIGDATARESPONSE']._serialized_options = b'\030\001'
  _globals['_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY']._loaded_options = None
  _globals['_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY']._serialized_options = b'8\001'
  _globals['_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY']._loaded_options = None
  _globals['_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY']._serialized_options = b'8\001'
  _globals['_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._loaded_options = None
  _globals['_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._serialized_options = b'8\001'
  _globals['_GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._loaded_options = None
  _globals['_GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._serialized_options = b'8\001'
  _globals['_ALMANACCONFIGSERVICE'].methods_by_name['GetConfigData']._loaded_options = None
  _globals['_ALMANACCONFIGSERVICE'].methods_by_name['GetConfigData']._serialized_options = b'\210\002\001'
  _globals['_GETCONFIGDATARESPONSE']._serialized_start=92
  _globals['_GETCONFIGDATARESPONSE']._serialized_end=464
  _globals['_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._serialized_start=346
  _globals['_GETCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._serialized_end=402
  _globals['_GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._serialized_start=404
  _globals['_GETCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._serialized_end=460
  _globals['_LOADALMANACCONFIGREQUEST']._serialized_start=466
  _globals['_LOADALMANACCONFIGREQUEST']._serialized_end=504
  _globals['_LOADALMANACCONFIGRESPONSE']._serialized_start=506
  _globals['_LOADALMANACCONFIGRESPONSE']._serialized_end=587
  _globals['_SAVEALMANACCONFIGREQUEST']._serialized_start=589
  _globals['_SAVEALMANACCONFIGREQUEST']._serialized_end=689
  _globals['_SAVEALMANACCONFIGRESPONSE']._serialized_start=691
  _globals['_SAVEALMANACCONFIGRESPONSE']._serialized_end=730
  _globals['_SETACTIVEALMANACCONFIGREQUEST']._serialized_start=732
  _globals['_SETACTIVEALMANACCONFIGREQUEST']._serialized_end=775
  _globals['_DELETEALMANACCONFIGREQUEST']._serialized_start=777
  _globals['_DELETEALMANACCONFIGREQUEST']._serialized_end=840
  _globals['_GETNEXTALMANACCONFIGRESPONSE']._serialized_start=843
  _globals['_GETNEXTALMANACCONFIGRESPONSE']._serialized_end=1064
  _globals['_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY']._serialized_start=1016
  _globals['_GETNEXTALMANACCONFIGRESPONSE_AVAILABLEENTRY']._serialized_end=1064
  _globals['_LOADDISCRIMINATORCONFIGREQUEST']._serialized_start=1066
  _globals['_LOADDISCRIMINATORCONFIGREQUEST']._serialized_end=1110
  _globals['_LOADDISCRIMINATORCONFIGRESPONSE']._serialized_start=1112
  _globals['_LOADDISCRIMINATORCONFIGRESPONSE']._serialized_end=1205
  _globals['_SAVEDISCRIMINATORCONFIGREQUEST']._serialized_start=1208
  _globals['_SAVEDISCRIMINATORCONFIGREQUEST']._serialized_end=1336
  _globals['_SAVEDISCRIMINATORCONFIGRESPONSE']._serialized_start=1338
  _globals['_SAVEDISCRIMINATORCONFIGRESPONSE']._serialized_end=1383
  _globals['_SETACTIVEDISCRIMINATORCONFIGREQUEST']._serialized_start=1385
  _globals['_SETACTIVEDISCRIMINATORCONFIGREQUEST']._serialized_end=1468
  _globals['_DELETEDISCRIMINATORCONFIGREQUEST']._serialized_start=1470
  _globals['_DELETEDISCRIMINATORCONFIGREQUEST']._serialized_end=1516
  _globals['_GETNEXTDISCRIMINATORCONFIGRESPONSE']._serialized_start=1519
  _globals['_GETNEXTDISCRIMINATORCONFIGRESPONSE']._serialized_end=1752
  _globals['_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY']._serialized_start=1016
  _globals['_GETNEXTDISCRIMINATORCONFIGRESPONSE_AVAILABLEENTRY']._serialized_end=1064
  _globals['_GETNEXTMODELINATORCONFIGRESPONSE']._serialized_start=1755
  _globals['_GETNEXTMODELINATORCONFIGRESPONSE']._serialized_end=1883
  _globals['_SAVEMODELINATORCONFIGREQUEST']._serialized_start=1885
  _globals['_SAVEMODELINATORCONFIGREQUEST']._serialized_end=1973
  _globals['_FETCHMODELINATORCONFIGREQUEST']._serialized_start=1975
  _globals['_FETCHMODELINATORCONFIGREQUEST']._serialized_end=2041
  _globals['_FETCHMODELINATORCONFIGRESPONSE']._serialized_start=2043
  _globals['_FETCHMODELINATORCONFIGRESPONSE']._serialized_end=2133
  _globals['_RESETMODELINATORCONFIGREQUEST']._serialized_start=2135
  _globals['_RESETMODELINATORCONFIGREQUEST']._serialized_end=2166
  _globals['_GETNEXTCONFIGDATAREQUEST']._serialized_start=2168
  _globals['_GETNEXTCONFIGDATAREQUEST']._serialized_end=2244
  _globals['_GETNEXTCONFIGDATARESPONSE']._serialized_start=2247
  _globals['_GETNEXTCONFIGDATARESPONSE']._serialized_end=2663
  _globals['_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._serialized_start=346
  _globals['_GETNEXTCONFIGDATARESPONSE_CROPCATEGORYNAMESENTRY']._serialized_end=402
  _globals['_GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._serialized_start=404
  _globals['_GETNEXTCONFIGDATARESPONSE_WEEDCATEGORYNAMESENTRY']._serialized_end=460
  _globals['_ALMANACCONFIGSERVICE']._serialized_start=2666
  _globals['_ALMANACCONFIGSERVICE']._serialized_end=4527
# @@protoc_insertion_point(module_scope)
