# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: recorder/recorder.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'recorder/recorder.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17recorder/recorder.proto\x12\x08recorder\".\n\x0e\x44\x65tectionClass\x12\r\n\x05\x63lass\x18\x01 \x01(\t\x12\r\n\x05score\x18\x02 \x01(\x02\"\xaf\x02\n\x11\x44\x65\x65pweedDetection\x12\t\n\x01x\x18\x01 \x01(\x02\x12\t\n\x01y\x18\x02 \x01(\x02\x12\x0c\n\x04size\x18\x03 \x01(\x02\x12\r\n\x05score\x18\x04 \x01(\x02\x12\x37\n\thit_class\x18\x05 \x01(\x0e\x32$.recorder.DeepweedDetection.HitClass\x12\x33\n\x11\x64\x65tection_classes\x18\x06 \x03(\x0b\x32\x18.recorder.DetectionClass\x12\x1a\n\x12mask_intersections\x18\x07 \x03(\x0c\x12\x15\n\rtrajectory_id\x18\x08 \x01(\r\x12\x12\n\nweed_score\x18\t \x01(\x02\x12\x12\n\ncrop_score\x18\n \x01(\x02\"\x1e\n\x08HitClass\x12\x08\n\x04WEED\x10\x00\x12\x08\n\x04\x43ROP\x10\x01\"`\n\x17\x44\x65\x65pweedPredictionFrame\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12/\n\ndetections\x18\x02 \x03(\x0b\x32\x1b.recorder.DeepweedDetection\"i\n\x18\x44\x65\x65pweedPredictionRecord\x12\x1b\n\x13record_timestamp_ms\x18\x01 \x01(\x04\x12\x30\n\x05\x66rame\x18\x02 \x01(\x0b\x32!.recorder.DeepweedPredictionFrame\">\n\x12LaneHeightSnapshot\x12\x13\n\x0bweed_height\x18\x01 \x03(\x01\x12\x13\n\x0b\x63rop_height\x18\x02 \x03(\x01\"_\n\x10LaneHeightRecord\x12\x1b\n\x13record_timestamp_ms\x18\x01 \x01(\x04\x12.\n\x08snapshot\x18\x02 \x01(\x0b\x32\x1c.recorder.LaneHeightSnapshot\"\xab\x01\n\x13RotaryTicksSnapshot\x12\x14\n\x0ctimestamp_us\x18\x01 \x01(\x04\x12\n\n\x02\x66l\x18\x02 \x01(\x05\x12\n\n\x02\x66r\x18\x03 \x01(\x05\x12\n\n\x02\x62l\x18\x04 \x01(\x05\x12\n\n\x02\x62r\x18\x05 \x01(\x05\x12\x12\n\nfl_enabled\x18\x06 \x01(\x08\x12\x12\n\nfr_enabled\x18\x07 \x01(\x08\x12\x12\n\nbl_enabled\x18\x08 \x01(\x08\x12\x12\n\nbr_enabled\x18\t \x01(\x08\"a\n\x11RotaryTicksRecord\x12\x1b\n\x13record_timestamp_ms\x18\x01 \x01(\x04\x12/\n\x08snapshot\x18\x02 \x01(\x0b\x32\<EMAIL>/carbonrobotics/protos/golang/generated/proto/recorderb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'recorder.recorder_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/carbonrobotics/protos/golang/generated/proto/recorder'
  _globals['_DETECTIONCLASS']._serialized_start=37
  _globals['_DETECTIONCLASS']._serialized_end=83
  _globals['_DEEPWEEDDETECTION']._serialized_start=86
  _globals['_DEEPWEEDDETECTION']._serialized_end=389
  _globals['_DEEPWEEDDETECTION_HITCLASS']._serialized_start=359
  _globals['_DEEPWEEDDETECTION_HITCLASS']._serialized_end=389
  _globals['_DEEPWEEDPREDICTIONFRAME']._serialized_start=391
  _globals['_DEEPWEEDPREDICTIONFRAME']._serialized_end=487
  _globals['_DEEPWEEDPREDICTIONRECORD']._serialized_start=489
  _globals['_DEEPWEEDPREDICTIONRECORD']._serialized_end=594
  _globals['_LANEHEIGHTSNAPSHOT']._serialized_start=596
  _globals['_LANEHEIGHTSNAPSHOT']._serialized_end=658
  _globals['_LANEHEIGHTRECORD']._serialized_start=660
  _globals['_LANEHEIGHTRECORD']._serialized_end=755
  _globals['_ROTARYTICKSSNAPSHOT']._serialized_start=758
  _globals['_ROTARYTICKSSNAPSHOT']._serialized_end=929
  _globals['_ROTARYTICKSRECORD']._serialized_start=931
  _globals['_ROTARYTICKSRECORD']._serialized_end=1028
# @@protoc_insertion_point(module_scope)
