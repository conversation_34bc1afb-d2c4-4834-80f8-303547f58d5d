# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/alarms.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/alarms.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2
from generated.frontend import alarm_pb2 as frontend_dot_alarm__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13portal/alarms.proto\x12\x14\x63\x61rbon.portal.alarms\x1a\x0fportal/db.proto\x1a\x14\x66rontend/alarm.proto\"\x99\x01\n\rAlarmResponse\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12.\n\x05\x61larm\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\x12\x12\n\nstarted_at\x18\x0b \x01(\x03\x12\x10\n\x08\x65nded_at\x18\x0c \x01(\x03\x12\x10\n\x08robot_id\x18\r \x01(\x03\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.alarms_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_ALARMRESPONSE']._serialized_start=85
  _globals['_ALARMRESPONSE']._serialized_end=238
# @@protoc_insertion_point(module_scope)
