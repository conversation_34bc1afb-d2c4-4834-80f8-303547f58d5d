# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/configs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/configs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.config.api import config_service_pb2 as config_dot_api_dot_config__service__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14portal/configs.proto\x12\x15\x63\x61rbon.portal.configs\x1a\x1f\x63onfig/api/config_service.proto\"\xc1\x01\n\x0e\x43onfigResponse\x12/\n\x06\x63onfig\x18\x01 \x01(\x0b\x32\x1f.carbon.config.proto.ConfigNode\x12\x36\n\x08template\x18\x02 \x01(\x0b\x32\x1f.carbon.config.proto.ConfigNodeH\x00\x88\x01\x01\x12\x39\n\runsynced_keys\x18\x03 \x03(\x0b\x32\".carbon.portal.configs.UnsyncedKeyB\x0b\n\t_template\"L\n\x0bUnsyncedKey\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\n\n\x02ts\x18\x02 \x01(\x04\x12\x11\n\told_value\x18\x03 \x01(\t\x12\x11\n\tnew_value\x18\x04 \x01(\t\"{\n\nConfigCrop\x12\x13\n\x0b\x63ommon_name\x18\x01 \x01(\t\x12\x13\n\x0b\x63\x61rbon_name\x18\x02 \x01(\t\x12\n\n\x02id\x18\x03 \x01(\t\x12\x0e\n\x06pinned\x18\x04 \x01(\t\x12\x13\n\x0brecommended\x18\x05 \x01(\t\x12\x12\n\nis_enabled\x18\x06 \x01(\x08\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.configs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_CONFIGRESPONSE']._serialized_start=81
  _globals['_CONFIGRESPONSE']._serialized_end=274
  _globals['_UNSYNCEDKEY']._serialized_start=276
  _globals['_UNSYNCEDKEY']._serialized_end=352
  _globals['_CONFIGCROP']._serialized_start=354
  _globals['_CONFIGCROP']._serialized_end=477
# @@protoc_insertion_point(module_scope)
