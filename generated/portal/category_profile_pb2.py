# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/category_profile.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/category_profile.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.category_profile import category_profile_pb2 as category__profile_dot_category__profile__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dportal/category_profile.proto\x12\x1e\x63\x61rbon.portal.category_profile\x1a\'category_profile/category_profile.proto\"2\n\x08Metadata\x12\x17\n\nupdated_at\x18\x06 \x01(\x03H\x00\x88\x01\x01\x42\r\n\x0b_updated_at\"\x96\x01\n\x1a\x43\x61tegoryCollectionResponse\x12<\n\x07profile\x18\x01 \x01(\x0b\x32+.carbon.category_profile.CategoryCollection\x12:\n\x08metadata\x18\x02 \x01(\x0b\x32(.carbon.portal.category_profile.Metadata\"\x82\x01\n\x10\x43\x61tegoryResponse\x12\x32\n\x07profile\x18\x01 \x01(\x0b\x32!.carbon.category_profile.Category\x12:\n\x08metadata\x18\x02 \x01(\x0b\x32(.carbon.portal.category_profile.Metadata\"\xb7\x01\n\"ExpandedCategoryCollectionResponse\x12K\n\x07profile\x18\x01 \x01(\x0b\x32:.carbon.portal.category_profile.CategoryCollectionResponse\x12\x44\n\ncategories\x18\x02 \x03(\x0b\x32\x30.carbon.portal.category_profile.CategoryResponse\"\x98\x01\n!ExpandedCategoryCollectionRequest\x12<\n\x07profile\x18\x01 \x01(\x0b\x32+.carbon.category_profile.CategoryCollection\x12\x35\n\ncategories\x18\x02 \x03(\x0b\x32!.carbon.category_profile.CategoryB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.category_profile_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_METADATA']._serialized_start=106
  _globals['_METADATA']._serialized_end=156
  _globals['_CATEGORYCOLLECTIONRESPONSE']._serialized_start=159
  _globals['_CATEGORYCOLLECTIONRESPONSE']._serialized_end=309
  _globals['_CATEGORYRESPONSE']._serialized_start=312
  _globals['_CATEGORYRESPONSE']._serialized_end=442
  _globals['_EXPANDEDCATEGORYCOLLECTIONRESPONSE']._serialized_start=445
  _globals['_EXPANDEDCATEGORYCOLLECTIONRESPONSE']._serialized_end=628
  _globals['_EXPANDEDCATEGORYCOLLECTIONREQUEST']._serialized_start=631
  _globals['_EXPANDEDCATEGORYCOLLECTIONREQUEST']._serialized_end=783
# @@protoc_insertion_point(module_scope)
