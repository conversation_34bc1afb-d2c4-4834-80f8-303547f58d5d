# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/users.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/users.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import customers_pb2 as portal_dot_customers__pb2
from generated.portal import db_pb2 as portal_dot_db__pb2
from generated.portal import auth_pb2 as portal_dot_auth__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12portal/users.proto\x12\x13\x63\x61rbon.portal.users\x1a\x16portal/customers.proto\x1a\x0fportal/db.proto\x1a\x11portal/auth.proto\"\xb7\x02\n\x0b\x41ppMetadata\x12\x18\n\x0b\x63ustomer_id\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x19\n\x0cis_activated\x18\x02 \x01(\x08H\x01\x88\x01\x01\x12\x17\n\nis_invited\x18\x03 \x01(\x08H\x02\x88\x01\x01\x12\x15\n\x08is_robot\x18\x04 \x01(\x08H\x03\x88\x01\x01\x12\x13\n\x0bpermissions\x18\x05 \x03(\t\x12\x1c\n\x0fsfdc_account_id\x18\x06 \x01(\x03H\x04\x88\x01\x01\x12\x36\n\x04role\x18\x07 \x01(\x0e\x32#.carbon.portal.auth.UserDisplayRoleH\x05\x88\x01\x01\x42\x0e\n\x0c_customer_idB\x0f\n\r_is_activatedB\r\n\x0b_is_invitedB\x0b\n\t_is_robotB\x12\n\x10_sfdc_account_idB\x07\n\x05_role\"\xc6\x02\n\x0cUserMetadata\x12\x1a\n\rconsent_given\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x1e\n\x11\x63onsent_timestamp\x18\x02 \x01(\x03H\x01\x88\x01\x01\x12\x19\n\x0c\x65xperimental\x18\x03 \x01(\x08H\x02\x88\x01\x01\x12\x15\n\x08language\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x18\n\x0bshow_mascot\x18\x05 \x01(\x08H\x04\x88\x01\x01\x12\x11\n\x04unit\x18\x06 \x01(\tH\x05\x88\x01\x01\x12\"\n\x15\x64\x65\x66\x61ult_fleet_view_id\x18\x07 \x01(\x03H\x06\x88\x01\x01\x42\x10\n\x0e_consent_givenB\x14\n\x12_consent_timestampB\x0f\n\r_experimentalB\x0b\n\t_languageB\x0e\n\x0c_show_mascotB\x07\n\x05_unitB\x18\n\x16_default_fleet_view_id\"\xac\x01\n\x0c\x41uth0Profile\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x16\n\x0e\x65mail_verified\x18\x02 \x01(\x08\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x10\n\x08username\x18\x04 \x01(\t\x12\x12\n\ngiven_name\x18\x05 \x01(\t\x12\x14\n\x0cphone_number\x18\x06 \x01(\t\x12\x16\n\x0ephone_verified\x18\x07 \x01(\x08\x12\x13\n\x0b\x66\x61mily_name\x18\x08 \x01(\t\"\xdb\x01\n\rAuth0Identity\x12\x12\n\nconnection\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\x12\x10\n\x08provider\x18\x03 \x01(\t\x12\x11\n\tis_social\x18\x04 \x01(\x08\x12\x14\n\x0c\x61\x63\x63\x65ss_token\x18\x05 \x01(\t\x12\x1b\n\x13\x61\x63\x63\x65ss_token_secret\x18\x06 \x01(\t\x12\x15\n\rrefresh_token\x18\x07 \x01(\t\x12\x36\n\x0bprofileData\x18\x08 \x01(\x0b\x32!.carbon.portal.users.Auth0Profile\"\x90\x04\n\tAuth0User\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x16\n\x0e\x65mail_verified\x18\x03 \x01(\x08\x12\x10\n\x08username\x18\x04 \x01(\t\x12\x14\n\x0cphone_number\x18\x05 \x01(\t\x12\x16\n\x0ephone_verified\x18\x06 \x01(\x08\x12\x12\n\ncreated_at\x18\x07 \x01(\t\x12\x12\n\nupdated_at\x18\x08 \x01(\t\x12\x36\n\nidentities\x18\t \x03(\x0b\x32\".carbon.portal.users.Auth0Identity\x12\x36\n\x0c\x61pp_metadata\x18\n \x01(\x0b\x32 .carbon.portal.users.AppMetadata\x12\x38\n\ruser_metadata\x18\x0b \x01(\x0b\x32!.carbon.portal.users.UserMetadata\x12\x0f\n\x07picture\x18\x0c \x01(\t\x12\x0c\n\x04name\x18\r \x01(\t\x12\x10\n\x08nickname\x18\x0e \x01(\t\x12\x13\n\x0bmultifactor\x18\x0f \x03(\t\x12\x0f\n\x07last_ip\x18\x10 \x01(\t\x12\x12\n\nlast_login\x18\x11 \x01(\t\x12\x14\n\x0clogins_count\x18\x12 \x01(\x03\x12\x0f\n\x07\x62locked\x18\x13 \x01(\x08\x12\x12\n\ngiven_name\x18\x14 \x01(\t\x12\x13\n\x0b\x66\x61mily_name\x18\x15 \x01(\t\"\xbf\x02\n\tFleetView\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x15\n\ruser_auth0_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0f\n\x07\x63olumns\x18\x04 \x03(\t\x12\x0e\n\x06search\x18\x05 \x01(\t\x12\x15\n\rshow_internal\x18\x06 \x01(\x08\x12\x10\n\x08show_map\x18\x07 \x01(\x08\x12\x14\n\x0cshow_offline\x18\x08 \x01(\x08\x12\x10\n\x08statuses\x18\t \x03(\t\x12\x18\n\x10pinned_robot_ids\x18\n \x03(\x03\x12\x17\n\x0b\x63ustomer_id\x18\x0b \x01(\x03\x42\x02\x18\x01\x12\x30\n\tview_mode\x18\x0c \x01(\x0e\x32\x1d.carbon.portal.users.ViewMode\x12\x14\n\x0c\x63ustomer_ids\x18\r \x03(\x03\"\xe4\x01\n\x0cUserResponse\x12,\n\x04user\x18\x01 \x01(\x0b\x32\x1e.carbon.portal.users.Auth0User\x12\x35\n\x0bpermissions\x18\x04 \x01(\x0b\x32 .carbon.portal.users.Permissions\x12;\n\x08\x63ustomer\x18\x02 \x01(\x0b\x32).carbon.portal.customers.CustomerResponse\x12\x32\n\nfleetViews\x18\x03 \x03(\x0b\x32\x1e.carbon.portal.users.FleetView\"\x1c\n\x0bPermissions\x12\r\n\x05names\x18\x01 \x03(\t\"#\n\x0fIsCarbonRequest\x12\x10\n\x08user_ids\x18\x01 \x03(\t\"D\n\x10IsCarbonResponse\x12\x30\n\x05users\x18\x01 \x03(\x0b\x32!.carbon.portal.users.IsCarbonUser\"2\n\x0cIsCarbonUser\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\tis_carbon\x18\x02 \x01(\x08*O\n\x08ViewMode\x12\x19\n\x15VIEW_MODE_UNSPECIFIED\x10\x00\x12\x13\n\x0fVIEW_MODE_CARDS\x10\x01\x12\x13\n\x0fVIEW_MODE_TABLE\x10\x02\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.users_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_FLEETVIEW'].fields_by_name['customer_id']._loaded_options = None
  _globals['_FLEETVIEW'].fields_by_name['customer_id']._serialized_options = b'\030\001'
  _globals['_VIEWMODE']._serialized_start=2416
  _globals['_VIEWMODE']._serialized_end=2495
  _globals['_APPMETADATA']._serialized_start=104
  _globals['_APPMETADATA']._serialized_end=415
  _globals['_USERMETADATA']._serialized_start=418
  _globals['_USERMETADATA']._serialized_end=744
  _globals['_AUTH0PROFILE']._serialized_start=747
  _globals['_AUTH0PROFILE']._serialized_end=919
  _globals['_AUTH0IDENTITY']._serialized_start=922
  _globals['_AUTH0IDENTITY']._serialized_end=1141
  _globals['_AUTH0USER']._serialized_start=1144
  _globals['_AUTH0USER']._serialized_end=1672
  _globals['_FLEETVIEW']._serialized_start=1675
  _globals['_FLEETVIEW']._serialized_end=1994
  _globals['_USERRESPONSE']._serialized_start=1997
  _globals['_USERRESPONSE']._serialized_end=2225
  _globals['_PERMISSIONS']._serialized_start=2227
  _globals['_PERMISSIONS']._serialized_end=2255
  _globals['_ISCARBONREQUEST']._serialized_start=2257
  _globals['_ISCARBONREQUEST']._serialized_end=2292
  _globals['_ISCARBONRESPONSE']._serialized_start=2294
  _globals['_ISCARBONRESPONSE']._serialized_end=2362
  _globals['_ISCARBONUSER']._serialized_start=2364
  _globals['_ISCARBONUSER']._serialized_end=2414
# @@protoc_insertion_point(module_scope)
