# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/db.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/db.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0fportal/db.proto\x12\x10\x63\x61rbon.portal.db\"L\n\x02\x44\x42\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x12\n\ncreated_at\x18\x02 \x01(\x03\x12\x12\n\nupdated_at\x18\x03 \x01(\x03\x12\x12\n\ndeleted_at\x18\x04 \x01(\x03\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.db_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_DB']._serialized_start=37
  _globals['_DB']._serialized_end=113
# @@protoc_insertion_point(module_scope)
