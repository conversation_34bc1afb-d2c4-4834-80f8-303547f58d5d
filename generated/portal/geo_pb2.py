# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/geo.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/geo.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.geo import geo_pb2 as geo_dot_geo__pb2
from generated.portal import db_pb2 as portal_dot_db__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10portal/geo.proto\x12\x13\x63\x61rbon.portal.field\x1a\rgeo/geo.proto\x1a\x0fportal/db.proto\"\x1a\n\x07GeoJSON\x12\x0f\n\x07\x66\x65\x61ture\x18\x01 \x01(\t\"\xa1\x01\n\x0fPlanPathRequest\x12\x0e\n\x06serial\x18\x05 \x01(\t\x12\x10\n\x08\x66ield_id\x18\x01 \x01(\t\x12(\n\rcurrent_point\x18\x02 \x01(\x0b\x32\x11.carbon.geo.Point\x12+\n\x0btarget_line\x18\x03 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\x15\n\rbuffer_meters\x18\x04 \x01(\x01\"R\n\x10PlanPathResponse\x12%\n\x05track\x18\x01 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\x17\n\x0f\x64istance_meters\x18\x02 \x01(\x01\"\xcd\x01\n\x0f\x46ieldDefinition\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x10\n\x08\x66ield_id\x18\x02 \x01(\t\x12\x10\n\x08robot_id\x18\x03 \x01(\x03\x12\x0c\n\x04name\x18\x04 \x01(\t\x12.\n\x08\x62oundary\x18\x05 \x01(\x0b\x32\x1c.carbon.portal.field.GeoJSON\x12\x36\n\x10planting_heading\x18\x06 \x01(\x0b\x32\x1c.carbon.portal.field.GeoJSONB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.geo_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_GEOJSON']._serialized_start=73
  _globals['_GEOJSON']._serialized_end=99
  _globals['_PLANPATHREQUEST']._serialized_start=102
  _globals['_PLANPATHREQUEST']._serialized_end=263
  _globals['_PLANPATHRESPONSE']._serialized_start=265
  _globals['_PLANPATHRESPONSE']._serialized_end=347
  _globals['_FIELDDEFINITION']._serialized_start=350
  _globals['_FIELDDEFINITION']._serialized_end=555
# @@protoc_insertion_point(module_scope)
