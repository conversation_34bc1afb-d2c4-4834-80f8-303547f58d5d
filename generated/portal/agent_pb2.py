# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/agent.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/agent.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.portal import messages_pb2 as portal_dot_messages__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12portal/agent.proto\x12\x13\x63\x61rbon.portal.agent\x1a\x0futil/util.proto\x1a\x15portal/messages.proto\"1\n\x0eMessageRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0e\n\x06serial\x18\x02 \x01(\t\">\n\x0fMessagesRequest\x12\x0c\n\x04page\x18\x01 \x01(\x03\x12\r\n\x05limit\x18\x02 \x01(\x03\x12\x0e\n\x06serial\x18\x03 \x01(\t\"A\n\x0bPushRequest\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12\x0e\n\x06serial\x18\x02 \x01(\t\"x\n\x0cPushResponse\x12\"\n\x02ts\x18\x01 \x01(\x0b\x32\x16.carbon.util.Timestamp\x12<\n\x08messages\x18\x02 \x01(\x0b\x32(.carbon.portal.messages.MessagesResponseH\x00\x42\x06\n\x04\x62ody2\x8e\x02\n\rAgentsService\x12R\n\x0bGetNextPush\x12 .carbon.portal.agent.PushRequest\x1a!.carbon.portal.agent.PushResponse\x12]\n\x0bGetMessages\x12$.carbon.portal.agent.MessagesRequest\x1a(.carbon.portal.messages.MessagesResponse\x12J\n\x0bSendMessage\x12#.carbon.portal.agent.MessageRequest\x1a\x16.carbon.util.TimestampB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.agent_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_MESSAGEREQUEST']._serialized_start=83
  _globals['_MESSAGEREQUEST']._serialized_end=132
  _globals['_MESSAGESREQUEST']._serialized_start=134
  _globals['_MESSAGESREQUEST']._serialized_end=196
  _globals['_PUSHREQUEST']._serialized_start=198
  _globals['_PUSHREQUEST']._serialized_end=263
  _globals['_PUSHRESPONSE']._serialized_start=265
  _globals['_PUSHRESPONSE']._serialized_end=385
  _globals['_AGENTSSERVICE']._serialized_start=388
  _globals['_AGENTSSERVICE']._serialized_end=658
# @@protoc_insertion_point(module_scope)
