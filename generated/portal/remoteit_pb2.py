# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/remoteit.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/remoteit.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15portal/remoteit.proto\x12\x16\x63\x61rbon.portal.remoteit\"9\n\x10\x43onfigureRequest\x12\x11\n\tserviceId\x18\x01 \x01(\t\x12\x12\n\ndeviceName\x18\x02 \x01(\t\"!\n\x0f\x43onfigureResult\x12\x0e\n\x06status\x18\x01 \x01(\t2s\n\x0fRemoteItManager\x12`\n\tConfigure\x12(.carbon.portal.remoteit.ConfigureRequest\x1a\'.carbon.portal.remoteit.ConfigureResult\"\x00\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.remoteit_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_CONFIGUREREQUEST']._serialized_start=49
  _globals['_CONFIGUREREQUEST']._serialized_end=106
  _globals['_CONFIGURERESULT']._serialized_start=108
  _globals['_CONFIGURERESULT']._serialized_end=141
  _globals['_REMOTEITMANAGER']._serialized_start=143
  _globals['_REMOTEITMANAGER']._serialized_end=258
# @@protoc_insertion_point(module_scope)
