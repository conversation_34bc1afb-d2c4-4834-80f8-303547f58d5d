# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/farm.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/farm.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.geo import geo_pb2 as geo_dot_geo__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11portal/farm.proto\x12\x12\x63\x61rbon.portal.farm\x1a\rgeo/geo.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd9\x01\n\x04\x46\x61rm\x12\x1a\n\x02id\x18\x01 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x30\n\x07version\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.VersionInfo\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x04 \x01(\x03\x12\x37\n\npoint_defs\x18\x05 \x03(\x0b\x32#.carbon.portal.farm.PointDefinition\x12\'\n\x05zones\x18\x06 \x03(\x0b\x32\x18.carbon.portal.farm.Zone\"q\n\x0bVersionInfo\x12\x0f\n\x07ordinal\x18\x01 \x01(\x03\x12/\n\x0bupdate_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07\x64\x65leted\x18\x03 \x01(\x08\x12\x0f\n\x07\x63hanged\x18\x04 \x01(\x08\"e\n\x0fPointDefinition\x12 \n\x05point\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x30\n\x07version\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.VersionInfo\"\xbf\x01\n\x04Zone\x12\x1a\n\x02id\x18\x01 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x30\n\x07version\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.VersionInfo\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\'\n\x05\x61reas\x18\x04 \x03(\x0b\x32\x18.carbon.portal.farm.Area\x12\x32\n\x08\x63ontents\x18\x05 \x01(\x0b\x32 .carbon.portal.farm.ZoneContents\"\xae\x02\n\x0cZoneContents\x12=\n\rfarm_boundary\x18\t \x01(\x0b\x32$.carbon.portal.farm.FarmBoundaryDataH\x00\x12.\n\x05\x66ield\x18\x05 \x01(\x0b\x32\x1d.carbon.portal.farm.FieldDataH\x00\x12\x34\n\x08headland\x18\x06 \x01(\x0b\x32 .carbon.portal.farm.HeadlandDataH\x00\x12;\n\x0cprivate_road\x18\x07 \x01(\x0b\x32#.carbon.portal.farm.PrivateRoadDataH\x00\x12\x34\n\x08obstacle\x18\x08 \x01(\x0b\x32 .carbon.portal.farm.ObstacleDataH\x00\x42\x06\n\x04\x64\x61ta\"\xa4\x01\n\x04\x41rea\x12\x15\n\rbuffer_meters\x18\x01 \x01(\x01\x12\"\n\x05point\x18\x02 \x01(\x0b\x32\x11.carbon.geo.PointH\x00\x12-\n\x0bline_string\x18\x03 \x01(\x0b\x32\x16.carbon.geo.LineStringH\x00\x12&\n\x07polygon\x18\x04 \x01(\x0b\x32\x13.carbon.geo.PolygonH\x00\x42\n\n\x08geometry\"\x12\n\x10\x46\x61rmBoundaryData\"\x81\x01\n\tFieldData\x12=\n\x10planting_heading\x18\x01 \x01(\x0b\x32#.carbon.portal.farm.PlantingHeading\x12\x35\n\x0c\x63\x65nter_pivot\x18\x02 \x01(\x0b\x32\x1f.carbon.portal.farm.CenterPivot\"\x0e\n\x0cHeadlandData\"\x11\n\x0fPrivateRoadData\"\x0e\n\x0cObstacleData\"^\n\x0fPlantingHeading\x12\x19\n\x0f\x61zimuth_degrees\x18\x01 \x01(\x01H\x00\x12%\n\x07\x61\x62_line\x18\x02 \x01(\x0b\x32\x12.carbon.geo.AbLineH\x00\x42\t\n\x07heading\"y\n\x0b\x43\x65nterPivot\x12!\n\x06\x63\x65nter\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x14\n\x0cwidth_meters\x18\x02 \x01(\x01\x12\x15\n\rlength_meters\x18\x03 \x01(\x01\x12\x1a\n\x12\x65ndpoint_device_id\x18\x04 \x01(\t\";\n\x11\x43reateFarmRequest\x12&\n\x04\x66\x61rm\x18\x01 \x01(\x0b\x32\x18.carbon.portal.farm.Farm\";\n\x11UpdateFarmRequest\x12&\n\x04\x66\x61rm\x18\x01 \x01(\x0b\x32\x18.carbon.portal.farm.Farm\"&\n\x10ListFarmsRequest\x12\x12\n\npage_token\x18\x01 \x01(\t\"U\n\x11ListFarmsResponse\x12\'\n\x05\x66\x61rms\x18\x01 \x03(\x0b\x32\x18.carbon.portal.farm.Farm\x12\x17\n\x0fnext_page_token\x18\x02 \x01(\t\"c\n\x0eGetFarmRequest\x12\x1a\n\x02id\x18\x01 \x01(\x0b\x32\x0e.carbon.geo.Id\x12\x35\n\x11if_modified_since\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"9\n\x0fGetFarmResponse\x12&\n\x04\x66\x61rm\x18\x01 \x01(\x0b\x32\x18.carbon.portal.farm.Farm2b\n\x0c\x46\x61rmsService\x12R\n\x07GetFarm\x12\".carbon.portal.farm.GetFarmRequest\x1a#.carbon.portal.farm.GetFarmResponseB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.farm_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_FARM']._serialized_start=90
  _globals['_FARM']._serialized_end=307
  _globals['_VERSIONINFO']._serialized_start=309
  _globals['_VERSIONINFO']._serialized_end=422
  _globals['_POINTDEFINITION']._serialized_start=424
  _globals['_POINTDEFINITION']._serialized_end=525
  _globals['_ZONE']._serialized_start=528
  _globals['_ZONE']._serialized_end=719
  _globals['_ZONECONTENTS']._serialized_start=722
  _globals['_ZONECONTENTS']._serialized_end=1024
  _globals['_AREA']._serialized_start=1027
  _globals['_AREA']._serialized_end=1191
  _globals['_FARMBOUNDARYDATA']._serialized_start=1193
  _globals['_FARMBOUNDARYDATA']._serialized_end=1211
  _globals['_FIELDDATA']._serialized_start=1214
  _globals['_FIELDDATA']._serialized_end=1343
  _globals['_HEADLANDDATA']._serialized_start=1345
  _globals['_HEADLANDDATA']._serialized_end=1359
  _globals['_PRIVATEROADDATA']._serialized_start=1361
  _globals['_PRIVATEROADDATA']._serialized_end=1378
  _globals['_OBSTACLEDATA']._serialized_start=1380
  _globals['_OBSTACLEDATA']._serialized_end=1394
  _globals['_PLANTINGHEADING']._serialized_start=1396
  _globals['_PLANTINGHEADING']._serialized_end=1490
  _globals['_CENTERPIVOT']._serialized_start=1492
  _globals['_CENTERPIVOT']._serialized_end=1613
  _globals['_CREATEFARMREQUEST']._serialized_start=1615
  _globals['_CREATEFARMREQUEST']._serialized_end=1674
  _globals['_UPDATEFARMREQUEST']._serialized_start=1676
  _globals['_UPDATEFARMREQUEST']._serialized_end=1735
  _globals['_LISTFARMSREQUEST']._serialized_start=1737
  _globals['_LISTFARMSREQUEST']._serialized_end=1775
  _globals['_LISTFARMSRESPONSE']._serialized_start=1777
  _globals['_LISTFARMSRESPONSE']._serialized_end=1862
  _globals['_GETFARMREQUEST']._serialized_start=1864
  _globals['_GETFARMREQUEST']._serialized_end=1963
  _globals['_GETFARMRESPONSE']._serialized_start=1965
  _globals['_GETFARMRESPONSE']._serialized_end=2022
  _globals['_FARMSSERVICE']._serialized_start=2024
  _globals['_FARMSSERVICE']._serialized_end=2122
# @@protoc_insertion_point(module_scope)
