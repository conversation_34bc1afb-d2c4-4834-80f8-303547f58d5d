# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/customers.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/customers.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16portal/customers.proto\x12\x17\x63\x61rbon.portal.customers\x1a\x0fportal/db.proto\"\xd0\x01\n\x0c\x46\x65\x61tureFlags\x12\x0f\n\x07reports\x18\x01 \x01(\x08\x12\x0f\n\x07\x61lmanac\x18\x02 \x01(\x08\x12\x0c\n\x04jobs\x18\x03 \x01(\x08\x12\x1b\n\x13unvalidated_metrics\x18\x04 \x01(\x08\x12\x0f\n\x07spatial\x18\x05 \x01(\x08\x12\x1a\n\x12velocity_estimator\x18\x06 \x01(\x08\x12\x0f\n\x07\x65xplore\x18\x07 \x01(\x08\x12\x1b\n\x13\x63\x61tegory_collection\x18\x08 \x01(\x08\x12\x18\n\x10metrics_redesign\x18\t \x01(\x08\"\xc7\x02\n\x10\x43ustomerResponse\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x1b\n\x0fsfdc_account_id\x18\x03 \x01(\tB\x02\x18\x01\x12\x0e\n\x06\x65mails\x18\x04 \x03(\t\x12;\n\x0c\x66\x65\x61tureFlags\x18\x05 \x01(\x0b\x32%.carbon.portal.customers.FeatureFlags\x12\x1a\n\x12weekly_report_hour\x18\x06 \x01(\x03\x12\x19\n\x11weekly_report_day\x18\x07 \x01(\x03\x12\x1d\n\x15weekly_report_enabled\x18\x08 \x01(\x08\x12#\n\x1bweekly_report_lookback_days\x18\t \x01(\x03\x12\x1e\n\x16weekly_report_timezone\x18\n \x01(\tB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.customers_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_CUSTOMERRESPONSE'].fields_by_name['sfdc_account_id']._loaded_options = None
  _globals['_CUSTOMERRESPONSE'].fields_by_name['sfdc_account_id']._serialized_options = b'\030\001'
  _globals['_FEATUREFLAGS']._serialized_start=69
  _globals['_FEATUREFLAGS']._serialized_end=277
  _globals['_CUSTOMERRESPONSE']._serialized_start=280
  _globals['_CUSTOMERRESPONSE']._serialized_end=607
# @@protoc_insertion_point(module_scope)
