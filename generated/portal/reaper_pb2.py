# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/reaper.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/reaper.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import module_pb2 as frontend_dot_module__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13portal/reaper.proto\x12\x14\x63\x61rbon.portal.reaper\x1a\x15\x66rontend/module.proto\"\xa2\x01\n\x13ReaperConfiguration\x12@\n\x10\x61ssigned_modules\x18\x01 \x03(\x0b\x32&.carbon.frontend.module.ModuleIdentity\x12I\n\x18\x63urrent_robot_definition\x18\x02 \x01(\x0b\x32\'.carbon.frontend.module.RobotDefinition\"d\n UploadReaperConfigurationRequest\x12@\n\rconfiguration\x18\x01 \x01(\x0b\x32).carbon.portal.reaper.ReaperConfiguration\"#\n!UploadReaperConfigurationResponse2\xab\x01\n\x1aReaperConfigurationService\x12\x8c\x01\n\x19UploadReaperConfiguration\x12\x36.carbon.portal.reaper.UploadReaperConfigurationRequest\x1a\x37.carbon.portal.reaper.UploadReaperConfigurationResponseB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.reaper_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_REAPERCONFIGURATION']._serialized_start=69
  _globals['_REAPERCONFIGURATION']._serialized_end=231
  _globals['_UPLOADREAPERCONFIGURATIONREQUEST']._serialized_start=233
  _globals['_UPLOADREAPERCONFIGURATIONREQUEST']._serialized_end=333
  _globals['_UPLOADREAPERCONFIGURATIONRESPONSE']._serialized_start=335
  _globals['_UPLOADREAPERCONFIGURATIONRESPONSE']._serialized_end=370
  _globals['_REAPERCONFIGURATIONSERVICE']._serialized_start=373
  _globals['_REAPERCONFIGURATIONSERVICE']._serialized_end=544
# @@protoc_insertion_point(module_scope)
