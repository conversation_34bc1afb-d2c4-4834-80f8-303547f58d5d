# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/hardware.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/hardware.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15portal/hardware.proto\x12\x16\x63\x61rbon.portal.hardware\x1a\x0fportal/db.proto\"\x8d\x03\n\nLaserStats\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x10\n\x08robot_id\x18\x02 \x01(\x04\x12\x12\n\nrow_number\x18\x03 \x01(\x04\x12\x10\n\x08laser_id\x18\x04 \x01(\x04\x12\x11\n\tcamera_id\x18\x05 \x01(\t\x12\x14\n\x0claser_serial\x18\x06 \x01(\t\x12\x0e\n\x06\x66iring\x18\x07 \x01(\x08\x12\x0f\n\x07\x65nabled\x18\x08 \x01(\x08\x12\r\n\x05\x65rror\x18\t \x01(\x08\x12\x18\n\x10total_fire_count\x18\n \x01(\x04\x12\x1a\n\x12total_fire_time_ms\x18\x0b \x01(\x04\x12\x12\n\ndelta_temp\x18\x0c \x01(\x02\x12\x0f\n\x07\x63urrent\x18\r \x01(\x02\x12\x1c\n\x14target_trajectory_id\x18\x0e \x01(\r\x12\x14\n\x0clifetime_sec\x18\x0f \x01(\x04\x12\x13\n\x0bpower_level\x18\x10 \x01(\x02\x12\x14\n\x0cinstalled_at\x18\x11 \x01(\x04\x12\x12\n\nremoved_at\x18\x12 \x01(\x04\"\xcb\x01\n\x10HardwareResponse\x12\x32\n\x06lasers\x18\x01 \x03(\x0b\x32\".carbon.portal.hardware.LaserStats\x12O\n\x0chost_serials\x18\x02 \x03(\x0b\x32\x39.carbon.portal.hardware.HardwareResponse.HostSerialsEntry\x1a\x32\n\x10HostSerialsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.hardware_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_HARDWARERESPONSE_HOSTSERIALSENTRY']._loaded_options = None
  _globals['_HARDWARERESPONSE_HOSTSERIALSENTRY']._serialized_options = b'8\001'
  _globals['_LASERSTATS']._serialized_start=67
  _globals['_LASERSTATS']._serialized_end=464
  _globals['_HARDWARERESPONSE']._serialized_start=467
  _globals['_HARDWARERESPONSE']._serialized_end=670
  _globals['_HARDWARERESPONSE_HOSTSERIALSENTRY']._serialized_start=620
  _globals['_HARDWARERESPONSE_HOSTSERIALSENTRY']._serialized_end=670
# @@protoc_insertion_point(module_scope)
