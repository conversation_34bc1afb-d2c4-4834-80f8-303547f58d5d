# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/messages.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/messages.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15portal/messages.proto\x12\x16\x63\x61rbon.portal.messages\x1a\x0fportal/db.proto\"\xf8\x01\n\x07Message\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x16\n\x0e\x61uthor_user_id\x18\x03 \x01(\t\x12\x17\n\x0f\x61uthor_robot_id\x18\x04 \x01(\x03\x12\x19\n\x11recipient_user_id\x18\x05 \x01(\t\x12\x1d\n\x15recipient_customer_id\x18\x06 \x01(\x03\x12\x1a\n\x12recipient_robot_id\x18\x07 \x01(\x03\x12\x19\n\x11\x61uthor_user_email\x18\x08 \x01(\t\x12\x18\n\x10\x61uthor_user_name\x18\t \x01(\t\"b\n\x10MessagesResponse\x12\x0c\n\x04page\x18\x02 \x01(\x03\x12\r\n\x05limit\x18\x03 \x01(\x03\x12\x31\n\x08messages\x18\x04 \x03(\x0b\x32\x1f.carbon.portal.messages.MessageB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.messages_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_MESSAGE']._serialized_start=67
  _globals['_MESSAGE']._serialized_end=315
  _globals['_MESSAGESRESPONSE']._serialized_start=317
  _globals['_MESSAGESRESPONSE']._serialized_end=415
# @@protoc_insertion_point(module_scope)
