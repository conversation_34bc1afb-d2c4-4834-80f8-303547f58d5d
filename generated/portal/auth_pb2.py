# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/auth.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/auth.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11portal/auth.proto\x12\x12\x63\x61rbon.portal.auth*\x93\x01\n\x0fUserDisplayRole\x12\x10\n\x0cunknown_role\x10\x00\x12\x0e\n\nrobot_role\x10\x01\x12\x12\n\x0eoperator_basic\x10\x02\x12\x10\n\x0c\x66\x61rm_manager\x10\x03\x12\x0f\n\x0b\x63\x61rbon_tech\x10\x04\x12\x10\n\x0c\x63\x61rbon_basic\x10\x05\x12\x15\n\x11operator_advanced\x10\x06*<\n\x10PermissionAction\x12\x12\n\x0eunknown_action\x10\x00\x12\x08\n\x04read\x10\x01\x12\n\n\x06update\x10\x02*\xfd\t\n\x12PermissionResource\x12\x0b\n\x07unknown\x10\x00\x12\x13\n\x0f\x61larms_customer\x10\x01\x12\x13\n\x0f\x61larms_internal\x10\x02\x12\x0c\n\x08\x61lmanacs\x10\x03\x12\x15\n\x11operator_settings\x10\x04\x12\x0b\n\x07\x62\x61nding\x10\x05\x12\x11\n\rbanding_basic\x10\x06\x12\x14\n\x10\x62\x61nding_advanced\x10\x07\x12\x0f\n\x0b\x63\x61libration\x10\x08\x12\x0b\n\x07\x63\x61meras\x10\t\x12\x0b\n\x07\x63\x61pture\x10\n\x12\x08\n\x04\x63hat\x10\x0b\x12\x0b\n\x07\x63onfigs\x10\x0c\x12\t\n\x05\x63rops\x10\r\x12\x10\n\x0cmodels_basic\x10\x0e\x12\x11\n\rmodels_pinned\x10\x0f\x12\x13\n\x0fmodels_nickname\x10\x10\x12\x0e\n\nthresholds\x10\x11\x12\x0f\n\x0b\x64iagnostics\x10\x12\x12\n\n\x06guides\x10\x13\x12\x08\n\x04jobs\x10\x14\x12\x0b\n\x07\x63\x61ptcha\x10\x15\x12\x0e\n\nlasers_row\x10\x16\x12\x10\n\x0clasers_basic\x10\x17\x12\x12\n\x0elasers_disable\x10\x18\x12\x11\n\rlasers_enable\x10\x19\x12\x13\n\x0flasers_advanced\x10\x1a\x12\n\n\x06lasers\x10\x1b\x12\t\n\x05\x66\x65\x65\x64s\x10\x1c\x12\x13\n\x0fmodels_advanced\x10\x1d\x12\x0c\n\x08hardware\x10\x1e\x12\x0e\n\nquick_tune\x10\x1f\x12\x0c\n\x08robot_ui\x10 \x12\x0c\n\x08thinning\x10!\x12\x12\n\x0ethinning_basic\x10\"\x12\x15\n\x11thinning_advanced\x10#\x12\x12\n\x0esoftware_basic\x10$\x12\x15\n\x11software_advanced\x10%\x12\x17\n\x13velocity_estimators\x10&\x12\x17\n\x13vizualization_basic\x10\'\x12\x1a\n\x16vizualization_advanced\x10(\x12\x0b\n\x07metrics\x10)\x12\x08\n\x04mode\x10*\x12\x0f\n\x0bjobs_select\x10+\x12\x13\n\x0fportal_settings\x10,\x12\x0f\n\x0bportal_labs\x10-\x12\x16\n\x12shortcuts_internal\x10.\x12\x10\n\x0c\x61\x64min_alarms\x10/\x12\x0f\n\x0b\x61\x64min_cloud\x10\x30\x12\r\n\tcustomers\x10\x31\x12\t\n\x05users\x10\x32\x12\x10\n\x0cusers_invite\x10\x33\x12\x15\n\x11users_permissions\x10\x34\x12\x0b\n\x07reports\x10\x35\x12\n\n\x06robots\x10\x36\x12\x11\n\rrobots_assign\x10\x37\x12\x11\n\rrobots_status\x10\x38\x12\x11\n\rrobots_health\x10\x39\x12\x0b\n\x07veselka\x10:\x12\x14\n\x10metrics_internal\x10;\x12\x14\n\x10metrics_customer\x10<\x12\x0f\n\x0b\x63rops_basic\x10=\x12\x12\n\x0e\x63rops_advanced\x10>\x12\x12\n\x0e\x64iscriminators\x10?\x12\x10\n\x0coperator_map\x10@\x12\x0b\n\x07uploads\x10\x41\x12\t\n\x05\x66\x61rms\x10\x42\x12\n\n\x06images\x10\x43\x12\x14\n\x10\x61utotractor_jobs\x10\x44\x12\x1b\n\x17plant_category_profiles\x10\x45\x12\x12\n\x0eportal_globals\x10\x46\x12\x15\n\x11\x61utotractor_drive\x10G*k\n\x10PermissionDomain\x12\x12\n\x0eunknown_domain\x10\x00\x12\x08\n\x04none\x10\x01\x12\x08\n\x04self\x10\x02\x12\t\n\x05robot\x10\x03\x12\x0c\n\x08\x63ustomer\x10\x04\x12\x07\n\x03\x61ll\x10\x05\x12\r\n\ttemplates\x10\x06\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.auth_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_USERDISPLAYROLE']._serialized_start=42
  _globals['_USERDISPLAYROLE']._serialized_end=189
  _globals['_PERMISSIONACTION']._serialized_start=191
  _globals['_PERMISSIONACTION']._serialized_end=251
  _globals['_PERMISSIONRESOURCE']._serialized_start=254
  _globals['_PERMISSIONRESOURCE']._serialized_end=1531
  _globals['_PERMISSIONDOMAIN']._serialized_start=1533
  _globals['_PERMISSIONDOMAIN']._serialized_end=1640
# @@protoc_insertion_point(module_scope)
