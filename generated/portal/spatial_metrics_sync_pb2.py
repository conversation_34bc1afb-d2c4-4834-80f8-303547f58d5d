# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/spatial_metrics_sync.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/spatial_metrics_sync.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.metrics import metrics_pb2 as metrics_dot_metrics__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!portal/spatial_metrics_sync.proto\x12\x1d\x63\x61rbon.portal.spatial_metrics\x1a\x15metrics/metrics.proto\x1a\x0futil/util.proto\"c\n\x1eSyncSpatialMetricBlocksRequest\x12\x32\n\x06\x62locks\x18\x01 \x03(\x0b\x32\".carbon.metrics.SpatialMetricBlock\x12\r\n\x05robot\x18\x02 \x01(\t2\x89\x01\n\x19SpatialMetricsSyncService\x12l\n\x17SyncSpatialMetricBlocks\x12=.carbon.portal.spatial_metrics.SyncSpatialMetricBlocksRequest\x1a\x12.carbon.util.EmptyB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.spatial_metrics_sync_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_SYNCSPATIALMETRICBLOCKSREQUEST']._serialized_start=108
  _globals['_SYNCSPATIALMETRICBLOCKSREQUEST']._serialized_end=207
  _globals['_SPATIALMETRICSSYNCSERVICE']._serialized_start=210
  _globals['_SPATIALMETRICSSYNCSERVICE']._serialized_end=347
# @@protoc_insertion_point(module_scope)
