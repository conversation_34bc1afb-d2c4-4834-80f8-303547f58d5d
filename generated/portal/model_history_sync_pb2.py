# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/model_history_sync.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/model_history_sync.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fportal/model_history_sync.proto\x12\x1b\x63\x61rbon.portal.model_history\x1a\x0futil/util.proto\"\xd6\x01\n\nModelEvent\x12\x39\n\x04type\x18\x01 \x01(\x0e\x32+.carbon.portal.model_history.ModelEventType\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x16\n\x0emodel_nickname\x18\x03 \x01(\t\x12\x18\n\x10model_parameters\x18\x04 \x01(\t\x12\x12\n\nmodel_type\x18\x05 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x06 \x01(\t\x12\x0e\n\x06job_id\x18\x07 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x08 \x01(\x03\"b\n\x18UploadModelEventsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12\x37\n\x06\x65vents\x18\x02 \x03(\x0b\x32\'.carbon.portal.model_history.ModelEvent*\xc6\x01\n\x0eModelEventType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0f\n\x0bROBOT_START\x10\x01\x12\n\n\x06PINNED\x10\x02\x12\x0c\n\x08UNPINNED\x10\x03\x12\x0f\n\x0bRECOMMENDED\x10\x04\x12\r\n\tACTIVATED\x10\x05\x12\x13\n\x0fNICKNAME_CHANGE\x10\x06\x12\x13\n\x0fNICKNAME_DELETE\x10\x07\x12\x1c\n\x18\x44\x45\x46\x41ULT_PARAMETER_CHANGE\x10\x08\x12\x14\n\x10PARAMETER_CHANGE\x10\t2y\n\x17ModelHistorySyncService\x12^\n\x11UploadModelEvents\x12\x35.carbon.portal.model_history.UploadModelEventsRequest\x1a\x12.carbon.util.EmptyB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.model_history_sync_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_MODELEVENTTYPE']._serialized_start=399
  _globals['_MODELEVENTTYPE']._serialized_end=597
  _globals['_MODELEVENT']._serialized_start=82
  _globals['_MODELEVENT']._serialized_end=296
  _globals['_UPLOADMODELEVENTSREQUEST']._serialized_start=298
  _globals['_UPLOADMODELEVENTSREQUEST']._serialized_end=396
  _globals['_MODELHISTORYSYNCSERVICE']._serialized_start=599
  _globals['_MODELHISTORYSYNCSERVICE']._serialized_end=720
# @@protoc_insertion_point(module_scope)
