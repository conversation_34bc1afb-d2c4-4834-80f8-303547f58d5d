# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/metrics.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/metrics.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14portal/metrics.proto\x12\x15\x63\x61rbon.portal.metrics\x1a\x0fportal/db.proto\"\x9d\t\n\x13\x44\x61ilyMetricResponse\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0e\n\x06serial\x18\x02 \x01(\t\x12\x10\n\x08robot_id\x18\x03 \x01(\x04\x12\x0c\n\x04\x64\x61te\x18\x19 \x01(\t\x12\x0e\n\x06job_id\x18, \x01(\t\x12\x10\n\x08job_name\x18- \x01(\t\x12\x14\n\x0c\x61\x63res_weeded\x18\x04 \x01(\x02\x12\x15\n\ravg_speed_mph\x18\x05 \x01(\x02\x12\x18\n\x10\x61vg_weed_size_mm\x18\x06 \x01(\x02\x12\x1b\n\x13\x62\x61nding_config_name\x18\x07 \x01(\t\x12\x17\n\x0f\x62\x61nding_enabled\x18\x08 \x01(\x08\x12\x1f\n\x17\x63overage_speed_acres_hr\x18\t \x01(\x02\x12\x1e\n\x16\x64istance_weeded_meters\x18\n \x01(\x02\x12\x14\n\x0ckilled_weeds\x18\x0b \x01(\x03\x12\x14\n\x0cmissed_weeds\x18\x0c \x01(\x03\x12\x15\n\rskipped_weeds\x18\r \x01(\x03\x12\x17\n\x0ftime_efficiency\x18\x0e \x01(\x02\x12\x13\n\x0btotal_weeds\x18\x0f \x01(\x03\x12\x16\n\x0euptime_seconds\x18\x10 \x01(\x02\x12\x1a\n\x12weed_density_sq_ft\x18\x11 \x01(\x02\x12\x1e\n\x16weeding_uptime_seconds\x18\x12 \x01(\x02\x12\x1a\n\x12weeding_efficiency\x18\x13 \x01(\x02\x12\"\n\x1aweeds_type_count_broadleaf\x18\x14 \x01(\x03\x12\x1e\n\x16weeds_type_count_grass\x18\x15 \x01(\x03\x12!\n\x19weeds_type_count_offshoot\x18\x16 \x01(\x03\x12!\n\x19weeds_type_count_purslane\x18\x17 \x01(\x03\x12\x19\n\x11not_weeding_weeds\x18\x18 \x01(\x03\x12\x12\n\nkept_crops\x18\x1a \x01(\x03\x12\x14\n\x0cmissed_crops\x18\x1b \x01(\x03\x12\x14\n\x0cnot_thinning\x18\x1c \x01(\x03\x12\x13\n\x0bnot_weeding\x18\x1d \x01(\x03\x12\x15\n\rskipped_crops\x18\x1e \x01(\x03\x12\x15\n\rthinned_crops\x18\x1f \x01(\x03\x12\x13\n\x0btotal_crops\x18  \x01(\x03\x12\x1a\n\x12\x62\x61nding_percentage\x18! \x01(\x02\x12\x1b\n\x13thinning_efficiency\x18\" \x01(\x02\x12\x0f\n\x07\x63rop_id\x18# \x01(\t\x12\x0c\n\x04\x63rop\x18$ \x01(\t\x12\x1a\n\x12\x63rop_density_sq_ft\x18% \x01(\x02\x12\x18\n\x10\x61vg_crop_size_mm\x18& \x01(\x02\x12%\n\x1d\x61vg_targetable_req_laser_time\x18\' \x01(\x03\x12\'\n\x1f\x61vg_untargetable_req_laser_time\x18( \x01(\x03\x12\x13\n\x0bvalid_crops\x18) \x01(\x03\x12\x1e\n\x16operator_effectiveness\x18* \x01(\x02\x12#\n\x1btarget_weeding_time_seconds\x18+ \x01(\x03\"\xc9\x01\n\x1a\x44\x61ilyMetricsByDateResponse\x12O\n\x07metrics\x18\x01 \x03(\x0b\x32>.carbon.portal.metrics.DailyMetricsByDateResponse.MetricsEntry\x1aZ\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.carbon.portal.metrics.DailyMetricResponse:\x02\x38\x01\"\xde\x01\n!DailyMetricsByDateByRobotResponse\x12V\n\x07metrics\x18\x01 \x03(\x0b\x32\x45.carbon.portal.metrics.DailyMetricsByDateByRobotResponse.MetricsEntry\x1a\x61\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12@\n\x05value\x18\x02 \x01(\x0b\x32\x31.carbon.portal.metrics.DailyMetricsByDateResponse:\x02\x38\x01\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.metrics_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_DAILYMETRICSBYDATERESPONSE_METRICSENTRY']._loaded_options = None
  _globals['_DAILYMETRICSBYDATERESPONSE_METRICSENTRY']._serialized_options = b'8\001'
  _globals['_DAILYMETRICSBYDATEBYROBOTRESPONSE_METRICSENTRY']._loaded_options = None
  _globals['_DAILYMETRICSBYDATEBYROBOTRESPONSE_METRICSENTRY']._serialized_options = b'8\001'
  _globals['_DAILYMETRICRESPONSE']._serialized_start=65
  _globals['_DAILYMETRICRESPONSE']._serialized_end=1246
  _globals['_DAILYMETRICSBYDATERESPONSE']._serialized_start=1249
  _globals['_DAILYMETRICSBYDATERESPONSE']._serialized_end=1450
  _globals['_DAILYMETRICSBYDATERESPONSE_METRICSENTRY']._serialized_start=1360
  _globals['_DAILYMETRICSBYDATERESPONSE_METRICSENTRY']._serialized_end=1450
  _globals['_DAILYMETRICSBYDATEBYROBOTRESPONSE']._serialized_start=1453
  _globals['_DAILYMETRICSBYDATEBYROBOTRESPONSE']._serialized_end=1675
  _globals['_DAILYMETRICSBYDATEBYROBOTRESPONSE_METRICSENTRY']._serialized_start=1578
  _globals['_DAILYMETRICSBYDATEBYROBOTRESPONSE_METRICSENTRY']._serialized_end=1675
# @@protoc_insertion_point(module_scope)
