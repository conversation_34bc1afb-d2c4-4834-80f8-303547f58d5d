# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.portal import profile_sync_pb2 as portal_dot_profile__sync__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in portal/profile_sync_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class PortalProfileSyncServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetProfilesData = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData',
                request_serializer=portal_dot_profile__sync__pb2.GetProfilesDataRequest.SerializeToString,
                response_deserializer=portal_dot_profile__sync__pb2.GetProfilesDataResponse.FromString,
                _registered_method=True)
        self.UploadProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile',
                request_serializer=portal_dot_profile__sync__pb2.UploadProfileRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile',
                request_serializer=portal_dot_profile__sync__pb2.GetProfileRequest.SerializeToString,
                response_deserializer=portal_dot_profile__sync__pb2.GetProfileResponse.FromString,
                _registered_method=True)
        self.DeleteProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile',
                request_serializer=portal_dot_profile__sync__pb2.DeleteProfileRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.PurgeProfile = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile',
                request_serializer=portal_dot_profile__sync__pb2.PurgeProfileRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)
        self.GetSetActiveProfileCommands = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands',
                request_serializer=portal_dot_profile__sync__pb2.GetSetActiveProfileCommandsRequest.SerializeToString,
                response_deserializer=portal_dot_profile__sync__pb2.GetSetActiveProfileCommandsResponse.FromString,
                _registered_method=True)
        self.PurgeSetActiveProfileCommands = channel.unary_unary(
                '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands',
                request_serializer=portal_dot_profile__sync__pb2.PurgeSetActiveProfileCommandsRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Empty.FromString,
                _registered_method=True)


class PortalProfileSyncServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetProfilesData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSetActiveProfileCommands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeSetActiveProfileCommands(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PortalProfileSyncServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetProfilesData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfilesData,
                    request_deserializer=portal_dot_profile__sync__pb2.GetProfilesDataRequest.FromString,
                    response_serializer=portal_dot_profile__sync__pb2.GetProfilesDataResponse.SerializeToString,
            ),
            'UploadProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadProfile,
                    request_deserializer=portal_dot_profile__sync__pb2.UploadProfileRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfile,
                    request_deserializer=portal_dot_profile__sync__pb2.GetProfileRequest.FromString,
                    response_serializer=portal_dot_profile__sync__pb2.GetProfileResponse.SerializeToString,
            ),
            'DeleteProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProfile,
                    request_deserializer=portal_dot_profile__sync__pb2.DeleteProfileRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'PurgeProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeProfile,
                    request_deserializer=portal_dot_profile__sync__pb2.PurgeProfileRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetSetActiveProfileCommands': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSetActiveProfileCommands,
                    request_deserializer=portal_dot_profile__sync__pb2.GetSetActiveProfileCommandsRequest.FromString,
                    response_serializer=portal_dot_profile__sync__pb2.GetSetActiveProfileCommandsResponse.SerializeToString,
            ),
            'PurgeSetActiveProfileCommands': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeSetActiveProfileCommands,
                    request_deserializer=portal_dot_profile__sync__pb2.PurgeSetActiveProfileCommandsRequest.FromString,
                    response_serializer=util_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.profile_sync.PortalProfileSyncService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.portal.profile_sync.PortalProfileSyncService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PortalProfileSyncService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetProfilesData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfilesData',
            portal_dot_profile__sync__pb2.GetProfilesDataRequest.SerializeToString,
            portal_dot_profile__sync__pb2.GetProfilesDataResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UploadProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.profile_sync.PortalProfileSyncService/UploadProfile',
            portal_dot_profile__sync__pb2.UploadProfileRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.profile_sync.PortalProfileSyncService/GetProfile',
            portal_dot_profile__sync__pb2.GetProfileRequest.SerializeToString,
            portal_dot_profile__sync__pb2.GetProfileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.profile_sync.PortalProfileSyncService/DeleteProfile',
            portal_dot_profile__sync__pb2.DeleteProfileRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PurgeProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeProfile',
            portal_dot_profile__sync__pb2.PurgeProfileRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSetActiveProfileCommands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.profile_sync.PortalProfileSyncService/GetSetActiveProfileCommands',
            portal_dot_profile__sync__pb2.GetSetActiveProfileCommandsRequest.SerializeToString,
            portal_dot_profile__sync__pb2.GetSetActiveProfileCommandsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PurgeSetActiveProfileCommands(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.profile_sync.PortalProfileSyncService/PurgeSetActiveProfileCommands',
            portal_dot_profile__sync__pb2.PurgeSetActiveProfileCommandsRequest.SerializeToString,
            util_dot_util__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
