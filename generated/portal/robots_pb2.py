# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/robots.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/robots.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import alarms_pb2 as portal_dot_alarms__pb2
from generated.portal import customers_pb2 as portal_dot_customers__pb2
from generated.portal import db_pb2 as portal_dot_db__pb2
from generated.portal import health_pb2 as portal_dot_health__pb2
from generated.portal import metrics_pb2 as portal_dot_metrics__pb2
from generated.config.api import config_service_pb2 as config_dot_api_dot_config__service__pb2
from generated.frontend import status_bar_pb2 as frontend_dot_status__bar__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13portal/robots.proto\x12\x14\x63\x61rbon.portal.robots\x1a\x13portal/alarms.proto\x1a\x16portal/customers.proto\x1a\x0fportal/db.proto\x1a\x13portal/health.proto\x1a\x14portal/metrics.proto\x1a\x1f\x63onfig/api/config_service.proto\x1a\x19\x66rontend/status_bar.proto\"\xfa\x04\n\x11\x43\x61\x63hedRobotHealth\x12\x30\n\x08location\x18\x02 \x01(\x0b\x32\x1e.carbon.portal.health.Location\x12\r\n\x05model\x18\x03 \x01(\t\x12\x36\n\x0bperformance\x18\x05 \x01(\x0b\x32!.carbon.portal.health.Performance\x12\x13\n\x0breported_at\x18\x06 \x01(\x03\x12\x32\n\x06status\x18\t \x01(\x0e\x32\".carbon.frontend.status_bar.Status\x12\x19\n\x11status_changed_at\x18\n \x01(\x03\x12\x10\n\x04\x63rop\x18\x0b \x01(\tB\x02\x18\x01\x12\x0b\n\x03p2p\x18\x0c \x01(\t\x12\x18\n\x10software_version\x18\r \x01(\t\x12\x16\n\x0etarget_version\x18\x0e \x01(\t\x12\x1c\n\x14target_version_ready\x18\x0f \x01(\x08\x12P\n\rmetric_totals\x18\x11 \x03(\x0b\x32\x39.carbon.portal.robots.CachedRobotHealth.MetricTotalsEntry\x12\x37\n\x0c\x66ield_config\x18\x13 \x01(\x0b\x32!.carbon.portal.health.FieldConfig\x12\x0f\n\x07\x63rop_id\x18\x15 \x01(\t\x12\x1a\n\x12robot_runtime_240v\x18\x16 \x01(\r\x12\x1a\n\x12\x61\x63tive_alarm_count\x18\x17 \x01(\x03\x12\x10\n\x08timezone\x18\x18 \x01(\t\x1a\x33\n\x11MetricTotalsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x04:\x02\x38\x01\"\xcc\x02\n\rRobotResponse\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x37\n\x06health\x18\x02 \x01(\x0b\x32\'.carbon.portal.robots.CachedRobotHealth\x12\x0e\n\x06serial\x18\x03 \x01(\t\x12\x1d\n\x15implementation_status\x18\x04 \x01(\t\x12L\n\rfeature_flags\x18\x05 \x03(\x0b\x32\x35.carbon.portal.robots.RobotResponse.FeatureFlagsEntry\x12\x17\n\x0f\x63ustomer_serial\x18\x06 \x01(\t\x12\x15\n\rsupport_slack\x18\x07 \x01(\t\x1a\x33\n\x11\x46\x65\x61tureFlagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"\xa3\x03\n\x14RobotSummaryResponse\x12\x32\n\x05robot\x18\x01 \x01(\x0b\x32#.carbon.portal.robots.RobotResponse\x12\x37\n\x06\x61larms\x18\x03 \x03(\x0b\x32#.carbon.portal.alarms.AlarmResponseB\x02\x18\x01\x12\x34\n\x0bhealth_logs\x18\x04 \x03(\x0b\x32\x1f.carbon.portal.health.HealthLog\x12;\n\x08\x63ustomer\x18\x05 \x01(\x0b\x32).carbon.portal.customers.CustomerResponse\x12\x37\n\nalarm_list\x18\x06 \x03(\x0b\x32#.carbon.portal.alarms.AlarmResponse\x12\x41\n\rdaily_metrics\x18\x07 \x01(\x0b\x32*.carbon.portal.metrics.DailyMetricResponse\x12/\n\x06\x63onfig\x18\x08 \x01(\x0b\x32\x1f.carbon.config.proto.ConfigNode\"d\n\x17RobotSummaryListReponse\x12:\n\x06robots\x18\x01 \x03(\x0b\x32*.carbon.portal.robots.RobotSummaryResponse\x12\r\n\x05total\x18\x02 \x01(\x03\"\x1e\n\x0eRemoteResponse\x12\x0c\n\x04host\x18\x01 \x01(\tB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.robots_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_CACHEDROBOTHEALTH_METRICTOTALSENTRY']._loaded_options = None
  _globals['_CACHEDROBOTHEALTH_METRICTOTALSENTRY']._serialized_options = b'8\001'
  _globals['_CACHEDROBOTHEALTH'].fields_by_name['crop']._loaded_options = None
  _globals['_CACHEDROBOTHEALTH'].fields_by_name['crop']._serialized_options = b'\030\001'
  _globals['_ROBOTRESPONSE_FEATUREFLAGSENTRY']._loaded_options = None
  _globals['_ROBOTRESPONSE_FEATUREFLAGSENTRY']._serialized_options = b'8\001'
  _globals['_ROBOTSUMMARYRESPONSE'].fields_by_name['alarms']._loaded_options = None
  _globals['_ROBOTSUMMARYRESPONSE'].fields_by_name['alarms']._serialized_options = b'\030\001'
  _globals['_CACHEDROBOTHEALTH']._serialized_start=211
  _globals['_CACHEDROBOTHEALTH']._serialized_end=845
  _globals['_CACHEDROBOTHEALTH_METRICTOTALSENTRY']._serialized_start=794
  _globals['_CACHEDROBOTHEALTH_METRICTOTALSENTRY']._serialized_end=845
  _globals['_ROBOTRESPONSE']._serialized_start=848
  _globals['_ROBOTRESPONSE']._serialized_end=1180
  _globals['_ROBOTRESPONSE_FEATUREFLAGSENTRY']._serialized_start=1129
  _globals['_ROBOTRESPONSE_FEATUREFLAGSENTRY']._serialized_end=1180
  _globals['_ROBOTSUMMARYRESPONSE']._serialized_start=1183
  _globals['_ROBOTSUMMARYRESPONSE']._serialized_end=1602
  _globals['_ROBOTSUMMARYLISTREPONSE']._serialized_start=1604
  _globals['_ROBOTSUMMARYLISTREPONSE']._serialized_end=1704
  _globals['_REMOTERESPONSE']._serialized_start=1706
  _globals['_REMOTERESPONSE']._serialized_end=1736
# @@protoc_insertion_point(module_scope)
