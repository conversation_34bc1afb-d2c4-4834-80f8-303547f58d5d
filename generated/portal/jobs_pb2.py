# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/jobs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/jobs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import jobs_pb2 as frontend_dot_jobs__pb2
from generated.frontend import weeding_diagnostics_pb2 as frontend_dot_weeding__diagnostics__pb2
from generated.metrics import metrics_aggregator_service_pb2 as metrics_dot_metrics__aggregator__service__pb2
from generated.portal import db_pb2 as portal_dot_db__pb2
from generated.portal import metrics_pb2 as portal_dot_metrics__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11portal/jobs.proto\x12\x12\x63\x61rbon.portal.jobs\x1a\x13\x66rontend/jobs.proto\x1a\"frontend/weeding_diagnostics.proto\x1a(metrics/metrics_aggregator_service.proto\x1a\x0fportal/db.proto\x1a\x14portal/metrics.proto\x1a\x0futil/util.proto\"\xf9\x02\n\tPortalJob\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x10\n\x08robot_id\x18\x02 \x01(\x04\x12\x0e\n\x06job_id\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x14\n\x0ctimestamp_ms\x18\x05 \x01(\x03\x12\x17\n\x0f\x62\x61nding_profile\x18\x06 \x01(\t\x12\x18\n\x10thinning_profile\x18\x07 \x01(\t\x12\x19\n\x11stop_timestamp_ms\x18\x08 \x01(\x03\x12\x0f\n\x07\x61\x63reage\x18\t \x01(\x02\x12\x11\n\tcompleted\x18\n \x01(\x08\x12\x0f\n\x07\x63rop_id\x18\x0b \x01(\t\x12\x0f\n\x07\x61lmanac\x18\x0c \x01(\t\x12\x15\n\rdiscriminator\x18\r \x01(\t\x12;\n\x07metrics\x18\x0e \x01(\x0b\x32*.carbon.portal.metrics.DailyMetricResponse\x12\x0c\n\x04\x63rop\x18\x0f \x01(\t\x12\x0e\n\x06serial\x18\x10 \x01(\t\"I\n\x10UploadJobRequest\x12&\n\x03job\x18\x01 \x01(\x0b\x32\x19.carbon.frontend.jobs.Job\x12\r\n\x05robot\x18\x02 \x01(\t\"x\n\x1aUploadJobConfigDumpRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\x12K\n\nrootConfig\x18\x02 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot\"Y\n\x17UploadJobMetricsRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\x12/\n\njobMetrics\x18\x02 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics2\x8a\x02\n\x11PortalJobsService\x12\x45\n\tUploadJob\x12$.carbon.portal.jobs.UploadJobRequest\x1a\x12.carbon.util.Empty\x12Y\n\x13UploadJobConfigDump\x12..carbon.portal.jobs.UploadJobConfigDumpRequest\x1a\x12.carbon.util.Empty\x12S\n\x10UploadJobMetrics\x12+.carbon.portal.jobs.UploadJobMetricsRequest\x1a\x12.carbon.util.EmptyB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.jobs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_PORTALJOB']._serialized_start=197
  _globals['_PORTALJOB']._serialized_end=574
  _globals['_UPLOADJOBREQUEST']._serialized_start=576
  _globals['_UPLOADJOBREQUEST']._serialized_end=649
  _globals['_UPLOADJOBCONFIGDUMPREQUEST']._serialized_start=651
  _globals['_UPLOADJOBCONFIGDUMPREQUEST']._serialized_end=771
  _globals['_UPLOADJOBMETRICSREQUEST']._serialized_start=773
  _globals['_UPLOADJOBMETRICSREQUEST']._serialized_end=862
  _globals['_PORTALJOBSSERVICE']._serialized_start=865
  _globals['_PORTALJOBSSERVICE']._serialized_end=1131
# @@protoc_insertion_point(module_scope)
