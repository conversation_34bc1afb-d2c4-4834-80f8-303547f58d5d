# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/admin.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/admin.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12portal/admin.proto\x12\x13\x63\x61rbon.portal.admin\x1a\x0fportal/db.proto\"D\n\x12GlobalAllowedAlarm\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\"U\n\x11RobotAllowedAlarm\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08robot_id\x18\x03 \x01(\x03\"U\n\x11RobotBlockedAlarm\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x10\n\x08robot_id\x18\x03 \x01(\x03\"O\n\x14GlobalAlarmAllowlist\x12\x37\n\x06\x61larms\x18\x01 \x03(\x0b\x32\'.carbon.portal.admin.GlobalAllowedAlarm\"M\n\x13RobotAlarmAllowlist\x12\x36\n\x06\x61larms\x18\x01 \x03(\x0b\x32&.carbon.portal.admin.RobotAllowedAlarm\"M\n\x13RobotAlarmBlocklist\x12\x36\n\x06\x61larms\x18\x01 \x03(\x0b\x32&.carbon.portal.admin.RobotBlockedAlarm\"N\n\x10GlobalAlarmlists\x12:\n\x07\x61llowed\x18\x01 \x01(\x0b\x32).carbon.portal.admin.GlobalAlarmAllowlist\"\x87\x01\n\x0fRobotAlarmlists\x12\x39\n\x07\x61llowed\x18\x01 \x01(\x0b\x32(.carbon.portal.admin.RobotAlarmAllowlist\x12\x39\n\x07\x62locked\x18\x02 \x01(\x0b\x32(.carbon.portal.admin.RobotAlarmBlocklist\"Y\n\x0bMaintenance\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x17\n\x0frestrict_access\x18\x03 \x01(\x08\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.admin_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_GLOBALALLOWEDALARM']._serialized_start=60
  _globals['_GLOBALALLOWEDALARM']._serialized_end=128
  _globals['_ROBOTALLOWEDALARM']._serialized_start=130
  _globals['_ROBOTALLOWEDALARM']._serialized_end=215
  _globals['_ROBOTBLOCKEDALARM']._serialized_start=217
  _globals['_ROBOTBLOCKEDALARM']._serialized_end=302
  _globals['_GLOBALALARMALLOWLIST']._serialized_start=304
  _globals['_GLOBALALARMALLOWLIST']._serialized_end=383
  _globals['_ROBOTALARMALLOWLIST']._serialized_start=385
  _globals['_ROBOTALARMALLOWLIST']._serialized_end=462
  _globals['_ROBOTALARMBLOCKLIST']._serialized_start=464
  _globals['_ROBOTALARMBLOCKLIST']._serialized_end=541
  _globals['_GLOBALALARMLISTS']._serialized_start=543
  _globals['_GLOBALALARMLISTS']._serialized_end=621
  _globals['_ROBOTALARMLISTS']._serialized_start=624
  _globals['_ROBOTALARMLISTS']._serialized_end=759
  _globals['_MAINTENANCE']._serialized_start=761
  _globals['_MAINTENANCE']._serialized_end=850
# @@protoc_insertion_point(module_scope)
