# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/health.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/health.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend import alarm_pb2 as frontend_dot_alarm__pb2
from generated.frontend import laser_pb2 as frontend_dot_laser__pb2
from generated.frontend import status_bar_pb2 as frontend_dot_status__bar__pb2
from generated.metrics import metrics_pb2 as metrics_dot_metrics__pb2
from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13portal/health.proto\x12\x14\x63\x61rbon.portal.health\x1a\x14\x66rontend/alarm.proto\x1a\x14\x66rontend/laser.proto\x1a\x19\x66rontend/status_bar.proto\x1a\x15metrics/metrics.proto\x1a\x0futil/util.proto\"\xdb\x01\n\x08\x41larmRow\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x12\n\nalarm_code\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12/\n\x05level\x18\x04 \x01(\x0e\x32 .carbon.portal.health.AlarmLevel\x12\x12\n\nidentifier\x18\x05 \x01(\t\x12\x14\n\x0c\x61\x63knowledged\x18\x06 \x01(\x08\x12\x31\n\x06impact\x18\x07 \x01(\x0e\x32!.carbon.portal.health.AlarmImpact:\x02\x18\x01\"+\n\x08Location\x12\t\n\x01x\x18\x01 \x01(\x01\x12\t\n\x01y\x18\x02 \x01(\x01\x12\t\n\x01z\x18\x03 \x01(\x01\"\xad\x04\n\x0b\x46ieldConfig\x12\x17\n\x0f\x62\x61nding_enabled\x18\x01 \x01(\x08\x12\x17\n\x0f\x62\x61nding_dynamic\x18\x02 \x01(\x08\x12\x1a\n\x12\x61\x63tive_band_config\x18\x03 \x01(\t\x12!\n\x19\x61\x63tive_thinning_config_id\x18\x04 \x01(\t\x12\x15\n\ractive_job_id\x18\x05 \x01(\t\x12\x19\n\x11\x61\x63tive_almanac_id\x18\x06 \x01(\t\x12\x1f\n\x17\x61\x63tive_discriminator_id\x18\x07 \x01(\t\x12\x12\n\nis_weeding\x18\x08 \x01(\x08\x12\x13\n\x0bis_thinning\x18\t \x01(\x08\x12\x1f\n\x17\x61\x63tive_band_config_name\x18\n \x01(\t\x12#\n\x1b\x61\x63tive_thinning_config_name\x18\x0b \x01(\t\x12\x17\n\x0f\x61\x63tive_job_name\x18\x0c \x01(\t\x12\x1b\n\x13\x61\x63tive_almanac_name\x18\r \x01(\t\x12!\n\x19\x61\x63tive_discriminator_name\x18\x0e \x01(\t\x12\x1d\n\x15\x61\x63tive_modelinator_id\x18\x0f \x01(\t\x12$\n\x1c\x61\x63tive_velocity_estimator_id\x18\x10 \x01(\t\x12&\n\x1e\x61\x63tive_velocity_estimator_name\x18\x11 \x01(\t\x12%\n\x1d\x61\x63tive_category_collection_id\x18\x12 \x01(\t\"+\n\x08Versions\x12\x0f\n\x07\x63urrent\x18\x01 \x01(\t\x12\x0e\n\x06latest\x18\x02 \x01(\t\"e\n\x12WeedingPerformance\x12\x19\n\x11\x61rea_weeded_total\x18\x01 \x01(\x01\x12\x19\n\x11\x61rea_weeded_today\x18\x02 \x01(\x01\x12\x19\n\x11time_weeded_today\x18\x03 \x01(\x03\"H\n\x0bPerformance\x12\x39\n\x07weeding\x18\x01 \x01(\x0b\x32(.carbon.portal.health.WeedingPerformance\"\x80\x01\n\x0c\x44\x61ilyMetrics\x12@\n\x07metrics\x18\x01 \x03(\x0b\x32/.carbon.portal.health.DailyMetrics.MetricsEntry\x1a.\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xaa\x01\n\x07Metrics\x12\x46\n\rdaily_metrics\x18\x01 \x03(\x0b\x32/.carbon.portal.health.Metrics.DailyMetricsEntry\x1aW\n\x11\x44\x61ilyMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".carbon.portal.health.DailyMetrics:\x02\x38\x01\"\x88\t\n\tHealthLog\x12\x32\n\x06\x61larms\x18\x01 \x03(\x0b\x32\x1e.carbon.portal.health.AlarmRowB\x02\x18\x01\x12\x30\n\x08location\x18\x02 \x01(\x0b\x32\x1e.carbon.portal.health.Location\x12\r\n\x05model\x18\x03 \x01(\t\x12\x0e\n\x06models\x18\x04 \x03(\t\x12\x36\n\x0bperformance\x18\x05 \x01(\x0b\x32!.carbon.portal.health.Performance\x12\x13\n\x0breported_at\x18\x06 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x07 \x01(\t\x12\x32\n\x06status\x18\t \x01(\x0e\x32\".carbon.frontend.status_bar.Status\x12\x19\n\x11status_changed_at\x18\n \x01(\x03\x12\x10\n\x04\x63rop\x18\x0b \x01(\tB\x02\x18\x01\x12\x0b\n\x03p2p\x18\x0c \x01(\t\x12\x18\n\x10software_version\x18\r \x01(\t\x12\x16\n\x0etarget_version\x18\x0e \x01(\t\x12\x1c\n\x14target_version_ready\x18\x0f \x01(\x08\x12\x16\n\x0estatus_message\x18\x10 \x01(\t\x12H\n\rmetric_totals\x18\x11 \x03(\x0b\x32\x31.carbon.portal.health.HealthLog.MetricTotalsEntry\x12\x33\n\nalarm_list\x18\x12 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\x12\x37\n\x0c\x66ield_config\x18\x13 \x01(\x0b\x32!.carbon.portal.health.FieldConfig\x12.\n\x07metrics\x18\x14 \x01(\x0b\x32\x1d.carbon.portal.health.Metrics\x12\x0f\n\x07\x63rop_id\x18\x15 \x01(\t\x12\x1a\n\x12robot_runtime_240v\x18\x16 \x01(\r\x12:\n\x0blaser_state\x18\x17 \x01(\x0b\x32%.carbon.frontend.laser.LaserStateList\x12<\n\x12laser_change_times\x18\x18 \x01(\x0b\x32 .carbon.metrics.LaserChangeTimes\x12\x46\n\x0chost_serials\x18\x19 \x03(\x0b\x32\x30.carbon.portal.health.HealthLog.HostSerialsEntry\x12H\n\rfeature_flags\x18\x1a \x03(\x0b\x32\x31.carbon.portal.health.HealthLog.FeatureFlagsEntry\x1a\x33\n\x11MetricTotalsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x04:\x02\x38\x01\x1a\x32\n\x10HostSerialsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x33\n\x11\x46\x65\x61tureFlagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"]\n\x0fHistoryResponse\x12-\n\x04logs\x18\x01 \x03(\x0b\x32\x1f.carbon.portal.health.HealthLog\x12\x1b\n\x13query_limit_reached\x18\x02 \x01(\x08\"\xb2\x01\n\x0bIssueReport\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x14\n\x0cphone_number\x18\x02 \x01(\t\x12\x14\n\x0crobot_serial\x18\x03 \x01(\t\x12\x13\n\x0breported_at\x18\x04 \x01(\x03\x12\x10\n\x04\x63rop\x18\x05 \x01(\tB\x02\x18\x01\x12\x10\n\x08model_id\x18\x06 \x01(\t\x12\x18\n\x10software_version\x18\x07 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x08 \x01(\t*\x8e\x01\n\nAlarmLevel\x12\x15\n\rALARM_UNKNOWN\x10\x00\x1a\x02\x08\x01\x12\x16\n\x0e\x41LARM_CRITICAL\x10\x01\x1a\x02\x08\x01\x12\x12\n\nALARM_HIGH\x10\x02\x1a\x02\x08\x01\x12\x14\n\x0c\x41LARM_MEDIUM\x10\x03\x1a\x02\x08\x01\x12\x11\n\tALARM_LOW\x10\x04\x1a\x02\x08\x01\x12\x14\n\x0c\x41LARM_HIDDEN\x10\x05\x1a\x02\x08\x01*\x86\x01\n\x0b\x41larmImpact\x12\x18\n\x10IMPACT_UNDEFINED\x10\x00\x1a\x02\x08\x01\x12\x17\n\x0fIMPACT_CRITICAL\x10\x01\x1a\x02\x08\x01\x12\x16\n\x0eIMPACT_OFFLINE\x10\x02\x1a\x02\x08\x01\x12\x17\n\x0fIMPACT_DEGRADED\x10\x03\x1a\x02\x08\x01\x12\x13\n\x0bIMPACT_NONE\x10\x04\x1a\x02\x08\x01\x32\x97\x01\n\rHealthService\x12@\n\tLogHealth\x12\x1f.carbon.portal.health.HealthLog\x1a\x12.carbon.util.Empty\x12\x44\n\x0bReportIssue\x12!.carbon.portal.health.IssueReport\x1a\x12.carbon.util.EmptyB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.health_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_ALARMLEVEL'].values_by_name["ALARM_UNKNOWN"]._loaded_options = None
  _globals['_ALARMLEVEL'].values_by_name["ALARM_UNKNOWN"]._serialized_options = b'\010\001'
  _globals['_ALARMLEVEL'].values_by_name["ALARM_CRITICAL"]._loaded_options = None
  _globals['_ALARMLEVEL'].values_by_name["ALARM_CRITICAL"]._serialized_options = b'\010\001'
  _globals['_ALARMLEVEL'].values_by_name["ALARM_HIGH"]._loaded_options = None
  _globals['_ALARMLEVEL'].values_by_name["ALARM_HIGH"]._serialized_options = b'\010\001'
  _globals['_ALARMLEVEL'].values_by_name["ALARM_MEDIUM"]._loaded_options = None
  _globals['_ALARMLEVEL'].values_by_name["ALARM_MEDIUM"]._serialized_options = b'\010\001'
  _globals['_ALARMLEVEL'].values_by_name["ALARM_LOW"]._loaded_options = None
  _globals['_ALARMLEVEL'].values_by_name["ALARM_LOW"]._serialized_options = b'\010\001'
  _globals['_ALARMLEVEL'].values_by_name["ALARM_HIDDEN"]._loaded_options = None
  _globals['_ALARMLEVEL'].values_by_name["ALARM_HIDDEN"]._serialized_options = b'\010\001'
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_UNDEFINED"]._loaded_options = None
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_UNDEFINED"]._serialized_options = b'\010\001'
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_CRITICAL"]._loaded_options = None
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_CRITICAL"]._serialized_options = b'\010\001'
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_OFFLINE"]._loaded_options = None
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_OFFLINE"]._serialized_options = b'\010\001'
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_DEGRADED"]._loaded_options = None
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_DEGRADED"]._serialized_options = b'\010\001'
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_NONE"]._loaded_options = None
  _globals['_ALARMIMPACT'].values_by_name["IMPACT_NONE"]._serialized_options = b'\010\001'
  _globals['_ALARMROW']._loaded_options = None
  _globals['_ALARMROW']._serialized_options = b'\030\001'
  _globals['_DAILYMETRICS_METRICSENTRY']._loaded_options = None
  _globals['_DAILYMETRICS_METRICSENTRY']._serialized_options = b'8\001'
  _globals['_METRICS_DAILYMETRICSENTRY']._loaded_options = None
  _globals['_METRICS_DAILYMETRICSENTRY']._serialized_options = b'8\001'
  _globals['_HEALTHLOG_METRICTOTALSENTRY']._loaded_options = None
  _globals['_HEALTHLOG_METRICTOTALSENTRY']._serialized_options = b'8\001'
  _globals['_HEALTHLOG_HOSTSERIALSENTRY']._loaded_options = None
  _globals['_HEALTHLOG_HOSTSERIALSENTRY']._serialized_options = b'8\001'
  _globals['_HEALTHLOG_FEATUREFLAGSENTRY']._loaded_options = None
  _globals['_HEALTHLOG_FEATUREFLAGSENTRY']._serialized_options = b'8\001'
  _globals['_HEALTHLOG'].fields_by_name['alarms']._loaded_options = None
  _globals['_HEALTHLOG'].fields_by_name['alarms']._serialized_options = b'\030\001'
  _globals['_HEALTHLOG'].fields_by_name['crop']._loaded_options = None
  _globals['_HEALTHLOG'].fields_by_name['crop']._serialized_options = b'\030\001'
  _globals['_ISSUEREPORT'].fields_by_name['crop']._loaded_options = None
  _globals['_ISSUEREPORT'].fields_by_name['crop']._serialized_options = b'\030\001'
  _globals['_ALARMLEVEL']._serialized_start=2949
  _globals['_ALARMLEVEL']._serialized_end=3091
  _globals['_ALARMIMPACT']._serialized_start=3094
  _globals['_ALARMIMPACT']._serialized_end=3228
  _globals['_ALARMROW']._serialized_start=157
  _globals['_ALARMROW']._serialized_end=376
  _globals['_LOCATION']._serialized_start=378
  _globals['_LOCATION']._serialized_end=421
  _globals['_FIELDCONFIG']._serialized_start=424
  _globals['_FIELDCONFIG']._serialized_end=981
  _globals['_VERSIONS']._serialized_start=983
  _globals['_VERSIONS']._serialized_end=1026
  _globals['_WEEDINGPERFORMANCE']._serialized_start=1028
  _globals['_WEEDINGPERFORMANCE']._serialized_end=1129
  _globals['_PERFORMANCE']._serialized_start=1131
  _globals['_PERFORMANCE']._serialized_end=1203
  _globals['_DAILYMETRICS']._serialized_start=1206
  _globals['_DAILYMETRICS']._serialized_end=1334
  _globals['_DAILYMETRICS_METRICSENTRY']._serialized_start=1288
  _globals['_DAILYMETRICS_METRICSENTRY']._serialized_end=1334
  _globals['_METRICS']._serialized_start=1337
  _globals['_METRICS']._serialized_end=1507
  _globals['_METRICS_DAILYMETRICSENTRY']._serialized_start=1420
  _globals['_METRICS_DAILYMETRICSENTRY']._serialized_end=1507
  _globals['_HEALTHLOG']._serialized_start=1510
  _globals['_HEALTHLOG']._serialized_end=2670
  _globals['_HEALTHLOG_METRICTOTALSENTRY']._serialized_start=2514
  _globals['_HEALTHLOG_METRICTOTALSENTRY']._serialized_end=2565
  _globals['_HEALTHLOG_HOSTSERIALSENTRY']._serialized_start=2567
  _globals['_HEALTHLOG_HOSTSERIALSENTRY']._serialized_end=2617
  _globals['_HEALTHLOG_FEATUREFLAGSENTRY']._serialized_start=2619
  _globals['_HEALTHLOG_FEATUREFLAGSENTRY']._serialized_end=2670
  _globals['_HISTORYRESPONSE']._serialized_start=2672
  _globals['_HISTORYRESPONSE']._serialized_end=2765
  _globals['_ISSUEREPORT']._serialized_start=2768
  _globals['_ISSUEREPORT']._serialized_end=2946
  _globals['_HEALTHSERVICE']._serialized_start=3231
  _globals['_HEALTHSERVICE']._serialized_end=3382
# @@protoc_insertion_point(module_scope)
