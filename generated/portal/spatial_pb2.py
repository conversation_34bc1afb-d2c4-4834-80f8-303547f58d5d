# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/spatial.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/spatial.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.metrics import metrics_pb2 as metrics_dot_metrics__pb2
from generated.portal import db_pb2 as portal_dot_db__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14portal/spatial.proto\x12\x15\x63\x61rbon.portal.spatial\x1a\x15metrics/metrics.proto\x1a\x0fportal/db.proto\"t\n\rBlockResponse\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x31\n\x05\x62lock\x18\x02 \x01(\x0b\x32\".carbon.metrics.SpatialMetricBlock\x12\x0e\n\x06serial\x18\x03 \x01(\t\"c\n\x0e\x42locksResponse\x12\x34\n\x06\x62locks\x18\x01 \x03(\x0b\x32$.carbon.portal.spatial.BlockResponse\x12\x1b\n\x13query_limit_reached\x18\x02 \x01(\x08\"\xb5\x01\n\x14\x42locksByDateResponse\x12G\n\x06\x62locks\x18\x01 \x03(\x0b\x32\x37.carbon.portal.spatial.BlocksByDateResponse.BlocksEntry\x1aT\n\x0b\x42locksEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x34\n\x05value\x18\x02 \x01(\x0b\x32%.carbon.portal.spatial.BlocksResponse:\x02\x38\x01\"\xc9\x01\n\x1b\x42locksByDateByRobotResponse\x12N\n\x06\x62locks\x18\x01 \x03(\x0b\x32>.carbon.portal.spatial.BlocksByDateByRobotResponse.BlocksEntry\x1aZ\n\x0b\x42locksEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12:\n\x05value\x18\x02 \x01(\x0b\x32+.carbon.portal.spatial.BlocksByDateResponse:\x02\x38\x01\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.spatial_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_BLOCKSBYDATERESPONSE_BLOCKSENTRY']._loaded_options = None
  _globals['_BLOCKSBYDATERESPONSE_BLOCKSENTRY']._serialized_options = b'8\001'
  _globals['_BLOCKSBYDATEBYROBOTRESPONSE_BLOCKSENTRY']._loaded_options = None
  _globals['_BLOCKSBYDATEBYROBOTRESPONSE_BLOCKSENTRY']._serialized_options = b'8\001'
  _globals['_BLOCKRESPONSE']._serialized_start=87
  _globals['_BLOCKRESPONSE']._serialized_end=203
  _globals['_BLOCKSRESPONSE']._serialized_start=205
  _globals['_BLOCKSRESPONSE']._serialized_end=304
  _globals['_BLOCKSBYDATERESPONSE']._serialized_start=307
  _globals['_BLOCKSBYDATERESPONSE']._serialized_end=488
  _globals['_BLOCKSBYDATERESPONSE_BLOCKSENTRY']._serialized_start=404
  _globals['_BLOCKSBYDATERESPONSE_BLOCKSENTRY']._serialized_end=488
  _globals['_BLOCKSBYDATEBYROBOTRESPONSE']._serialized_start=491
  _globals['_BLOCKSBYDATEBYROBOTRESPONSE']._serialized_end=692
  _globals['_BLOCKSBYDATEBYROBOTRESPONSE_BLOCKSENTRY']._serialized_start=602
  _globals['_BLOCKSBYDATEBYROBOTRESPONSE_BLOCKSENTRY']._serialized_end=692
# @@protoc_insertion_point(module_scope)
