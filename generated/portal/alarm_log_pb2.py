# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/alarm_log.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/alarm_log.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2
from generated.frontend import alarm_pb2 as frontend_dot_alarm__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16portal/alarm_log.proto\x12\x17\x63\x61rbon.portal.alarm_log\x1a\x0futil/util.proto\x1a\x14\x66rontend/alarm.proto\"X\n\x11SyncAlarmsRequest\x12/\n\x06\x61larms\x18\x01 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\x12\x12\n\nrobot_name\x18\x02 \x01(\t2e\n\x15PortalAlarmLogService\x12L\n\nSyncAlarms\x12*.carbon.portal.alarm_log.SyncAlarmsRequest\x1a\x12.carbon.util.EmptyB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.alarm_log_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_SYNCALARMSREQUEST']._serialized_start=90
  _globals['_SYNCALARMSREQUEST']._serialized_end=178
  _globals['_PORTALALARMLOGSERVICE']._serialized_start=180
  _globals['_PORTALALARMLOGSERVICE']._serialized_end=281
# @@protoc_insertion_point(module_scope)
