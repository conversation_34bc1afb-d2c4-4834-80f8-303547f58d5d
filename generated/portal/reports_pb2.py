# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/reports.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/reports.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2
from generated.portal import customers_pb2 as portal_dot_customers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14portal/reports.proto\x12\x15\x63\x61rbon.portal.reports\x1a\x0fportal/db.proto\x1a\x16portal/customers.proto\"\xc6\x03\n\x16ReportInstanceResponse\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x11\n\tauthor_id\x18\x02 \x01(\t\x12\x12\n\nmanager_id\x18\x03 \x01(\t\x12\x11\n\treport_id\x18\x04 \x01(\x04\x12\x11\n\trobot_ids\x18\x05 \x03(\x04\x12\x12\n\nstart_date\x18\x06 \x01(\x03\x12\x10\n\x08\x65nd_date\x18\x07 \x01(\x03\x12\x16\n\x0epublish_emails\x18\x08 \x03(\t\x12\x0c\n\x04slug\x18\x0f \x01(\t\x12\x0c\n\x04name\x18\x10 \x01(\t\x12\x17\n\x0fvisible_columns\x18\x11 \x03(\t\x12\x11\n\torder_asc\x18\x12 \x01(\x08\x12\x10\n\x08order_by\x18\x13 \x01(\t\x12\x14\n\x0cshow_average\x18\x14 \x01(\x08\x12\x12\n\nshow_total\x18\x15 \x01(\x08\x12\x13\n\x0b\x61uthor_name\x18\x16 \x01(\t\x12\x11\n\tautomated\x18\x17 \x01(\x08\x12/\n\x04mode\x18\x18 \x01(\x0e\x32!.carbon.portal.reports.ReportModeJ\x04\x08\t\x10\nJ\x04\x08\n\x10\x0bJ\x04\x08\x0b\x10\x0cJ\x04\x08\x0c\x10\rJ\x04\x08\r\x10\x0eJ\x04\x08\x0e\x10\x0f\"\xd1\x03\n\x0eReportResponse\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x11\n\tauthor_id\x18\x02 \x01(\t\x12\x17\n\x0fvisible_columns\x18\x03 \x03(\t\x12\x11\n\torder_asc\x18\x04 \x01(\x08\x12\x10\n\x08order_by\x18\x05 \x01(\t\x12\x14\n\x0cshow_average\x18\x06 \x01(\x08\x12\x12\n\nshow_total\x18\x07 \x01(\x08\x12\x0c\n\x04slug\x18\r \x01(\t\x12\x0c\n\x04name\x18\x0e \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x0f \x01(\x04\x12\x11\n\trobot_ids\x18\x10 \x03(\x04\x12\x12\n\nstart_date\x18\x11 \x01(\x03\x12\x10\n\x08\x65nd_date\x18\x12 \x01(\x03\x12\x17\n\x0f\x61utomate_weekly\x18\x13 \x01(\x08\x12\x13\n\x0b\x61uthor_name\x18\x14 \x01(\t\x12;\n\x08\x63ustomer\x18\x15 \x01(\x0b\x32).carbon.portal.customers.CustomerResponse\x12/\n\x04mode\x18\x16 \x01(\x0e\x32!.carbon.portal.reports.ReportModeJ\x04\x08\x08\x10\tJ\x04\x08\t\x10\nJ\x04\x08\n\x10\x0bJ\x04\x08\x0b\x10\x0cJ\x04\x08\x0c\x10\r*U\n\nReportMode\x12\x1b\n\x17REPORT_MODE_UNSPECIFIED\x10\x00\x12\x14\n\x10REPORT_MODE_JOBS\x10\x01\x12\x14\n\x10REPORT_MODE_DAYS\x10\x02\x42@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.reports_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_REPORTMODE']._serialized_start=1013
  _globals['_REPORTMODE']._serialized_end=1098
  _globals['_REPORTINSTANCERESPONSE']._serialized_start=89
  _globals['_REPORTINSTANCERESPONSE']._serialized_end=543
  _globals['_REPORTRESPONSE']._serialized_start=546
  _globals['_REPORTRESPONSE']._serialized_end=1011
# @@protoc_insertion_point(module_scope)
