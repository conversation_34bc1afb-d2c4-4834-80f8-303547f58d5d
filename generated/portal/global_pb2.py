# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/global.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/global.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import field_mask_pb2 as google_dot_protobuf_dot_field__mask__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13portal/global.proto\x12\x14\x63\x61rbon.portal.global\x1a google/protobuf/field_mask.proto\")\n\x07Globals\x12\x1e\n\x16plant_profile_model_id\x18\x01 \x01(\t\"w\n\x14UpdateGlobalsRequest\x12.\n\x07globals\x18\x01 \x01(\x0b\x32\x1d.carbon.portal.global.Globals\x12/\n\x0bupdate_mask\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.FieldMaskB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.global_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_GLOBALS']._serialized_start=79
  _globals['_GLOBALS']._serialized_end=120
  _globals['_UPDATEGLOBALSREQUEST']._serialized_start=122
  _globals['_UPDATEGLOBALSREQUEST']._serialized_end=241
# @@protoc_insertion_point(module_scope)
