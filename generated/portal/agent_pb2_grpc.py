# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.portal import agent_pb2 as portal_dot_agent__pb2
from generated.portal import messages_pb2 as portal_dot_messages__pb2
from generated.util import util_pb2 as util_dot_util__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in portal/agent_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AgentsServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextPush = channel.unary_unary(
                '/carbon.portal.agent.AgentsService/GetNextPush',
                request_serializer=portal_dot_agent__pb2.PushRequest.SerializeToString,
                response_deserializer=portal_dot_agent__pb2.PushResponse.FromString,
                _registered_method=True)
        self.GetMessages = channel.unary_unary(
                '/carbon.portal.agent.AgentsService/GetMessages',
                request_serializer=portal_dot_agent__pb2.MessagesRequest.SerializeToString,
                response_deserializer=portal_dot_messages__pb2.MessagesResponse.FromString,
                _registered_method=True)
        self.SendMessage = channel.unary_unary(
                '/carbon.portal.agent.AgentsService/SendMessage',
                request_serializer=portal_dot_agent__pb2.MessageRequest.SerializeToString,
                response_deserializer=util_dot_util__pb2.Timestamp.FromString,
                _registered_method=True)


class AgentsServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextPush(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMessages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AgentsServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextPush': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPush,
                    request_deserializer=portal_dot_agent__pb2.PushRequest.FromString,
                    response_serializer=portal_dot_agent__pb2.PushResponse.SerializeToString,
            ),
            'GetMessages': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMessages,
                    request_deserializer=portal_dot_agent__pb2.MessagesRequest.FromString,
                    response_serializer=portal_dot_messages__pb2.MessagesResponse.SerializeToString,
            ),
            'SendMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.SendMessage,
                    request_deserializer=portal_dot_agent__pb2.MessageRequest.FromString,
                    response_serializer=util_dot_util__pb2.Timestamp.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.portal.agent.AgentsService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.portal.agent.AgentsService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AgentsService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextPush(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.agent.AgentsService/GetNextPush',
            portal_dot_agent__pb2.PushRequest.SerializeToString,
            portal_dot_agent__pb2.PushResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetMessages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.agent.AgentsService/GetMessages',
            portal_dot_agent__pb2.MessagesRequest.SerializeToString,
            portal_dot_messages__pb2.MessagesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SendMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.portal.agent.AgentsService/SendMessage',
            portal_dot_agent__pb2.MessageRequest.SerializeToString,
            util_dot_util__pb2.Timestamp.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
