# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/model_info_sync.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/model_info_sync.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.util import util_pb2 as util_dot_util__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cportal/model_info_sync.proto\x12\x18\x63\x61rbon.portal.model_info\x1a\x0futil/util.proto\"A\n\tModelInfo\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x10\n\x08\x63rop_ids\x18\x02 \x03(\t\x12\x10\n\x08nickname\x18\x03 \x01(\t\"b\n\x17UploadModelInfosRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12\x38\n\x0bmodel_infos\x18\x02 \x03(\x0b\x32#.carbon.portal.model_info.ModelInfo\"<\n\x12RenameModelCommand\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x14\n\x0cnew_nickname\x18\x02 \x01(\t\".\n\x1dGetRenameModelCommandsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\"`\n\x1eGetRenameModelCommandsResponse\x12>\n\x08\x63ommands\x18\x01 \x03(\x0b\x32,.carbon.portal.model_info.RenameModelCommand\"p\n\x1fPurgeRenameModelCommandsRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12>\n\x08\x63ommands\x18\x02 \x03(\x0b\x32,.carbon.portal.model_info.RenameModelCommand2\xea\x02\n\x14ModelInfoSyncService\x12Y\n\x10UploadModelInfos\x12\x31.carbon.portal.model_info.UploadModelInfosRequest\x1a\x12.carbon.util.Empty\x12\x8b\x01\n\x16GetRenameModelCommands\x12\x37.carbon.portal.model_info.GetRenameModelCommandsRequest\x1a\x38.carbon.portal.model_info.GetRenameModelCommandsResponse\x12i\n\x18PurgeRenameModelCommands\x12\x39.carbon.portal.model_info.PurgeRenameModelCommandsRequest\x1a\x12.carbon.util.EmptyB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.model_info_sync_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_MODELINFO']._serialized_start=75
  _globals['_MODELINFO']._serialized_end=140
  _globals['_UPLOADMODELINFOSREQUEST']._serialized_start=142
  _globals['_UPLOADMODELINFOSREQUEST']._serialized_end=240
  _globals['_RENAMEMODELCOMMAND']._serialized_start=242
  _globals['_RENAMEMODELCOMMAND']._serialized_end=302
  _globals['_GETRENAMEMODELCOMMANDSREQUEST']._serialized_start=304
  _globals['_GETRENAMEMODELCOMMANDSREQUEST']._serialized_end=350
  _globals['_GETRENAMEMODELCOMMANDSRESPONSE']._serialized_start=352
  _globals['_GETRENAMEMODELCOMMANDSRESPONSE']._serialized_end=448
  _globals['_PURGERENAMEMODELCOMMANDSREQUEST']._serialized_start=450
  _globals['_PURGERENAMEMODELCOMMANDSREQUEST']._serialized_end=562
  _globals['_MODELINFOSYNCSERVICE']._serialized_start=565
  _globals['_MODELINFOSYNCSERVICE']._serialized_end=927
# @@protoc_insertion_point(module_scope)
