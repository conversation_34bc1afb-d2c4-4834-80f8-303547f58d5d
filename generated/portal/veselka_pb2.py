# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: portal/veselka.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'portal/veselka.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal import db_pb2 as portal_dot_db__pb2
from generated.portal import category_profile_pb2 as portal_dot_category__profile__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14portal/veselka.proto\x12\x15\x63\x61rbon.portal.veselka\x1a\x0fportal/db.proto\x1a\x1dportal/category_profile.proto\"\xe5\x03\n\x05Image\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08location\x18\x02 \x01(\t\x12\x10\n\x08robot_id\x18\x03 \x01(\t\x12\x10\n\x08use_case\x18\x04 \x01(\t\x12\x0c\n\x04role\x18\x05 \x01(\t\x12\x0f\n\x07\x63reated\x18\x06 \x01(\x03\x12\x0b\n\x03url\x18\x07 \x01(\t\x12\x0e\n\x06height\x18\x08 \x01(\x03\x12\r\n\x05width\x18\t \x01(\x03\x12\x0b\n\x03ppi\x18\n \x01(\x03\x12\r\n\x05valid\x18\x0b \x01(\x03\x12\x13\n\x0b\x63\x61ptured_at\x18\x0c \x01(\x03\x12\x16\n\x0e\x64\x65tection_json\x18\r \x01(\t\x12\x13\n\x0breason_json\x18\x0e \x01(\t\x12\x10\n\x08priority\x18\x0f \x01(\t\x12\x0e\n\x06\x63\x61m_id\x18\x10 \x01(\t\x12\x0e\n\x06row_id\x18\x11 \x01(\t\x12\x10\n\x08geo_json\x18\x12 \x01(\t\x12\x0c\n\x04\x63rop\x18\x13 \x01(\t\x12\x12\n\nimage_type\x18\x14 \x01(\t\x12\x0c\n\x04\x63ity\x18\x15 \x01(\t\x12\x14\n\x0c\x63orrected_hw\x18\x16 \x01(\x08\x12\x14\n\x0csession_name\x18\x17 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x18 \x01(\t\x12\x14\n\x0c\x66ocus_metric\x18\x19 \x01(\x03\x12\x0f\n\x07geohash\x18\x1a \x01(\t\x12\x19\n\x11quarantine_reason\x18\x1b \x01(\t\"\xd8\x01\n\x0e\x43ropConfidence\x12 \n\x02\x64\x62\x18\x01 \x01(\x0b\x32\x14.carbon.portal.db.DB\x12\x10\n\x08robot_id\x18\x02 \x01(\x03\x12\x0f\n\x07\x63rop_id\x18\x03 \x01(\t\x12\x10\n\x08latitude\x18\x04 \x01(\x02\x12\x11\n\tlongitude\x18\x05 \x01(\x02\x12\x11\n\tprecision\x18\x06 \x01(\x03\x12\x18\n\x10num_total_images\x18\x07 \x01(\x03\x12\x1b\n\x13num_regional_images\x18\x08 \x01(\x03\x12\x12\n\nconfidence\x18\t \x01(\t\"\xae\x01\n\x04\x43rop\x12\x10\n\x08\x61rchived\x18\x01 \x01(\x08\x12\x13\n\x0b\x63\x61rbon_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63ommon_name\x18\x03 \x01(\t\x12\x0f\n\x07\x63reated\x18\x04 \x01(\x03\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\n\n\x02id\x18\x06 \x01(\t\x12\x18\n\x10legacy_crop_name\x18\x07 \x01(\t\x12\r\n\x05notes\x18\x08 \x01(\t\x12\x0f\n\x07updated\x18\t \x01(\x03\"q\n\tRobotCrop\x12)\n\x04\x63rop\x18\x01 \x01(\x0b\x32\x1b.carbon.portal.veselka.Crop\x12\x39\n\nconfidence\x18\x02 \x01(\x0b\x32%.carbon.portal.veselka.CropConfidence\"\x99\x05\n\x05Model\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07\x63reated\x18\x02 \x01(\x03\x12\x0f\n\x07updated\x18\x03 \x01(\x03\x12\x0b\n\x03url\x18\x04 \x01(\t\x12\x10\n\x08\x63ustomer\x18\x05 \x01(\t\x12\x0c\n\x04\x63rop\x18\x06 \x01(\t\x12\x0f\n\x07version\x18\x07 \x01(\x03\x12\x1b\n\x13training_docker_tag\x18\x08 \x01(\t\x12\x0e\n\x06gitSha\x18\t \x01(\t\x12\x10\n\x08\x63hecksum\x18\n \x01(\t\x12\x10\n\x08location\x18\x0b \x01(\t\x12\x12\n\ntrained_at\x18\x0c \x01(\x03\x12\x0c\n\x04type\x18\r \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x0e \x01(\t\x12\x15\n\rmetadata_json\x18\x0f \x01(\t\x12$\n\x1cproduction_container_version\x18\x10 \x01(\t\x12\x19\n\x11test_results_json\x18\x11 \x01(\t\x12\x12\n\nwandb_json\x18\x12 \x01(\t\x12\x15\n\rsnapshot_json\x18\x13 \x01(\t\x12\x0f\n\x07is_stub\x18\x14 \x01(\x08\x12\x19\n\x11is_good_to_deploy\x18\x15 \x01(\x08\x12\x12\n\nrobot_name\x18\x16 \x01(\t\x12\x13\n\x0b\x65nvironment\x18\x17 \x01(\t\x12\x0e\n\x06\x64\x65ploy\x18\x18 \x01(\x08\x12\x16\n\x0eis_pretraining\x18\x19 \x01(\x08\x12\x10\n\x08sub_type\x18\x1a \x01(\t\x12\x12\n\ndataset_id\x18\x1b \x01(\t\x12\x19\n\x11\x63ontainer_version\x18\x1c \x01(\t\x12\x14\n\x0c\x63ontainer_id\x18\x1d \x01(\t\x12\x13\n\x0bpipeline_id\x18\x1e \x01(\t\x12\x17\n\x0fparent_model_id\x18\x1f \x01(\t\x12\x17\n\x0fviable_crop_ids\x18  \x03(\t\"\xcc\x01\n&CreateCategoryCollectionSessionRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x18\n\x0b\x63ustomer_id\x18\x02 \x01(\x04H\x00\x88\x01\x01\x12\x66\n\x1b\x63\x61tegory_collection_profile\x18\x03 \x01(\x0b\x32\x41.carbon.portal.category_profile.ExpandedCategoryCollectionRequestB\x0e\n\x0c_customer_idB@Z>github.com/carbonrobotics/protos/golang/generated/proto/portalb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'portal.veselka_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z>github.com/carbonrobotics/protos/golang/generated/proto/portal'
  _globals['_IMAGE']._serialized_start=96
  _globals['_IMAGE']._serialized_end=581
  _globals['_CROPCONFIDENCE']._serialized_start=584
  _globals['_CROPCONFIDENCE']._serialized_end=800
  _globals['_CROP']._serialized_start=803
  _globals['_CROP']._serialized_end=977
  _globals['_ROBOTCROP']._serialized_start=979
  _globals['_ROBOTCROP']._serialized_end=1092
  _globals['_MODEL']._serialized_start=1095
  _globals['_MODEL']._serialized_end=1760
  _globals['_CREATECATEGORYCOLLECTIONSESSIONREQUEST']._serialized_start=1763
  _globals['_CREATECATEGORYCOLLECTIONSESSIONREQUEST']._serialized_end=1967
# @@protoc_insertion_point(module_scope)
