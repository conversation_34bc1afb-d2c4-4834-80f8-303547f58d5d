# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: rtc/jobs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'rtc/jobs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from generated.geo import geo_pb2 as geo_dot_geo__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ertc/jobs.proto\x12\ncarbon.rtc\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\rgeo/geo.proto\"\xca\x02\n\tObjective\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x18\n\x10progress_percent\x18\x04 \x01(\x05\x12\x10\n\x08priority\x18\x05 \x01(\x05\x12\x31\n\x04type\x18\x06 \x01(\x0e\x32#.carbon.rtc.Objective.ObjectiveType\x12%\n\x04\x64\x61ta\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x33\n\nassignment\x18\x08 \x01(\x0b\x32\x1f.carbon.rtc.ObjectiveAssignment\x12\x0e\n\x06job_id\x18\t \x01(\x04\"C\n\rObjectiveType\x12\x1e\n\x1aOBJECTIVE_TYPE_UNSPECIFIED\x10\x00\x12\x12\n\x0eLASER_WEED_ROW\x10\x01\"Q\n\x19LaserWeedRowObjectiveData\x12\x0f\n\x07row_num\x18\x01 \x01(\r\x12#\n\x07\x61\x62_line\x18\x02 \x01(\x0b\x32\x12.carbon.geo.AbLine\":\n\rObjectiveList\x12)\n\nobjectives\x18\x01 \x03(\x0b\x32\x15.carbon.rtc.Objective\"W\n\x16ListObjectivesResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12)\n\nobjectives\x18\x02 \x03(\x0b\x32\x15.carbon.rtc.Objective\"5\n\x1dGetNextActiveObjectiveRequest\x12\x14\n\x0cobjective_id\x18\x01 \x01(\x04\"J\n\x1eGetNextActiveObjectiveResponse\x12(\n\tobjective\x18\x02 \x01(\x0b\x32\x15.carbon.rtc.Objective\"M\n\x13ObjectiveAssignment\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cobjective_id\x18\x02 \x01(\x04\x12\x14\n\x0crobot_serial\x18\x03 \x01(\t\"O\n\x17ObjectiveAssignmentList\x12\x34\n\x0b\x61ssignments\x18\x01 \x03(\x0b\x32\x1f.carbon.rtc.ObjectiveAssignment\"l\n ListObjectiveAssignmentsResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12\x34\n\x0b\x61ssignments\x18\x02 \x03(\x0b\x32\x1f.carbon.rtc.ObjectiveAssignment\"+\n\x13RobotWhitelistEntry\x12\x14\n\x0crobot_serial\x18\x01 \x01(\t\"B\n\x0eRobotWhitelist\x12\x30\n\x07\x65ntries\x18\x01 \x03(\x0b\x32\x1f.carbon.rtc.RobotWhitelistEntry\"\xa1\x07\n\x04Task\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12.\n\nstarted_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nded_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x11\x65xpected_duration\x18\x05 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x13\n\x0bstatus_info\x18\x06 \x01(\t\x12\x38\n\x16\x65xpected_tractor_state\x18\x07 \x03(\x0b\x32\x18.carbon.rtc.TractorState\x12 \n\x05state\x18\x08 \x01(\x0e\x32\x11.carbon.rtc.State\x12\x10\n\x08priority\x18\t \x01(\x05\x12\x14\n\x0cobjective_id\x18\x10 \x01(\x04\x12)\n\x0estart_location\x18\x12 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x15\n\rstart_heading\x18\x13 \x01(\x01\x12\'\n\x0c\x65nd_location\x18\x14 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x13\n\x0b\x65nd_heading\x18\x15 \x01(\x01\x12\x19\n\x11manually_assisted\x18\x17 \x01(\x08\x12,\n\x08sequence\x18\n \x01(\x0b\x32\x18.carbon.rtc.SequenceTaskH\x00\x12(\n\x06manual\x18\x0b \x01(\x0b\x32\x16.carbon.rtc.ManualTaskH\x00\x12\x35\n\x0ego_to_and_face\x18\x0c \x01(\x0b\x32\x1b.carbon.rtc.GoToAndFaceTaskH\x00\x12\x31\n\x0b\x66ollow_path\x18\r \x01(\x0b\x32\x1a.carbon.rtc.FollowPathTaskH\x00\x12\x38\n\rtractor_state\x18\x0e \x01(\x0b\x32\x1f.carbon.rtc.SetTractorStateTaskH\x00\x12/\n\nlaser_weed\x18\x0f \x01(\x0b\x32\x19.carbon.rtc.LaserWeedTaskH\x00\x12\x35\n\rstop_autonomy\x18\x11 \x01(\x0b\x32\x1c.carbon.rtc.StopAutonomyTaskH\x00\x12\x43\n\x15go_to_reversible_path\x18\x16 \x01(\x0b\x32\".carbon.rtc.GoToReversiblePathTaskH\x00\x42\x0e\n\x0ctask_details\"\x12\n\x10StopAutonomyTask\"\xba\x01\n\rLaserWeedTask\x12$\n\x04path\x18\x01 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\x1a\n\x12path_is_reversible\x18\x02 \x01(\x08\x12\x17\n\x0fweeding_enabled\x18\x03 \x01(\x08\x12\x18\n\x10thinning_enabled\x18\x04 \x01(\x08\x12\x34\n\ntolerances\x18\x06 \x01(\x0b\x32 .carbon.rtc.SpatialPathTolerance\"?\n\x0cSequenceTask\x12\x1f\n\x05items\x18\x01 \x03(\x0b\x32\x10.carbon.rtc.Task\x12\x0e\n\x06\x61tomic\x18\x02 \x01(\x08\"\"\n\nManualTask\x12\x14\n\x0cinstructions\x18\x01 \x01(\t\"D\n\x0fGoToAndFaceTask\x12 \n\x05point\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x0f\n\x07heading\x18\x02 \x01(\x01\"t\n\x16GoToReversiblePathTask\x12$\n\x04path\x18\x01 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\x34\n\ntolerances\x18\x02 \x01(\x0b\x32 .carbon.rtc.SpatialPathTolerance\"{\n\x0e\x46ollowPathTask\x12$\n\x04path\x18\x01 \x01(\x0b\x32\x16.carbon.geo.LineString\x12\'\n\x05speed\x18\x02 \x01(\x0b\x32\x18.carbon.rtc.SpeedSetting\x12\x1a\n\x12stop_on_completion\x18\x03 \x01(\x08\"\xa5\x01\n\x0cSpeedSetting\x12\x16\n\x0c\x63onstant_mph\x18\x01 \x01(\x01H\x00\x12<\n\x1aremote_operator_controlled\x18\x02 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x12\x36\n\x14implement_controlled\x18\x03 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x42\x07\n\x05speed\">\n\x13SetTractorStateTask\x12\'\n\x05state\x18\x01 \x03(\x0b\x32\x18.carbon.rtc.TractorState\"\xcd\x01\n\x0cTractorState\x12-\n\x04gear\x18\x01 \x01(\x0e\x32\x1d.carbon.rtc.TractorState.GearH\x00\x12\'\n\x05hitch\x18\x02 \x01(\x0b\x32\x16.carbon.rtc.HitchStateH\x00\"\\\n\x04Gear\x12\x14\n\x10GEAR_UNSPECIFIED\x10\x00\x12\x08\n\x04PARK\x10\x01\x12\x0b\n\x07REVERSE\x10\x02\x12\x0b\n\x07NEUTRAL\x10\x03\x12\x0b\n\x07\x46ORWARD\x10\x04\x12\r\n\tPOWERZERO\x10\x05\x42\x07\n\x05state\"\xa9\x01\n\nHitchState\x12\x36\n\x07\x63ommand\x18\x01 \x01(\x0e\x32#.carbon.rtc.HitchState.HitchCommandH\x00\x12\x12\n\x08position\x18\x02 \x01(\x01H\x00\"F\n\x0cHitchCommand\x12\x1d\n\x19HITCH_COMMAND_UNSPECIFIED\x10\x00\x12\n\n\x06RAISED\x10\x01\x12\x0b\n\x07LOWERED\x10\x02\x42\x07\n\x05state\"+\n\x08TaskList\x12\x1f\n\x05tasks\x18\x01 \x03(\x0b\x32\x10.carbon.rtc.Task\"H\n\x11ListTasksResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12\x1f\n\x05tasks\x18\x02 \x03(\x0b\x32\x10.carbon.rtc.Task\"l\n\x14SpatialPathTolerance\x12\x0f\n\x07heading\x18\x01 \x01(\x02\x12\x12\n\ncrosstrack\x18\x02 \x01(\x02\x12\x10\n\x08\x64istance\x18\x03 \x01(\x02\x12\x1d\n\x15\x63ontinuous_crosstrack\x18\x04 \x01(\x02\"\xd3\x03\n\x03Job\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12.\n\nstarted_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nded_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12)\n\nobjectives\x18\x05 \x03(\x0b\x32\x15.carbon.rtc.Objective\x12 \n\x05state\x18\x06 \x01(\x0e\x32\x11.carbon.rtc.State\x12%\n\x04type\x18\x07 \x01(\x0e\x32\x17.carbon.rtc.Job.JobType\x12\x1a\n\rwork_order_id\x18\x08 \x01(\x04H\x00\x88\x01\x01\x12\x0f\n\x07\x66\x61rm_id\x18\t \x01(\t\x12\x10\n\x08\x66ield_id\x18\n \x01(\t\x12\x13\n\x0b\x63ustomer_id\x18\x0b \x01(\t\x12\x10\n\x08priority\x18\x0c \x01(\x05\x12\x33\n\x0frobot_whitelist\x18\r \x01(\x0b\x32\x1a.carbon.rtc.RobotWhitelist\"3\n\x07JobType\x12\x18\n\x14JOB_TYPE_UNSPECIFIED\x10\x00\x12\x0e\n\nLASER_WEED\x10\x01\x42\x10\n\x0e_work_order_id\"(\n\x07JobList\x12\x1d\n\x04jobs\x18\x01 \x03(\x0b\x32\x0f.carbon.rtc.Job\"E\n\x10ListJobsResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12\x1d\n\x04jobs\x18\x02 \x03(\x0b\x32\x0f.carbon.rtc.Job\"\xb2\x01\n\tWorkOrder\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x30\n\x0cscheduled_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10\x64uration_minutes\x18\x04 \x01(\x05\x12\x1d\n\x04jobs\x18\x05 \x03(\x0b\x32\x0f.carbon.rtc.Job\x12 \n\x05state\x18\x06 \x01(\x0e\x32\x11.carbon.rtc.State\";\n\rWorkOrderList\x12*\n\x0bwork_orders\x18\x01 \x03(\x0b\x32\x15.carbon.rtc.WorkOrder\"X\n\x16ListWorkOrdersResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12*\n\x0bwork_orders\x18\x02 \x03(\x0b\x32\x15.carbon.rtc.WorkOrder\"\xa2\x03\n\x0cIntervention\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0f\n\x07task_id\x18\x02 \x01(\x04\x12\x15\n\rqualification\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12 \n\x05state\x18\x05 \x01(\x0e\x32\x11.carbon.rtc.State\x12\x14\n\x0crobot_serial\x18\x06 \x01(\t\x12\x0e\n\x06job_id\x18\x07 \x01(\x04\x12\x39\n\x05\x63\x61use\x18\x08 \x01(\x0e\x32*.carbon.rtc.Intervention.InterventionCause\x12\x10\n\x08priority\x18\t \x01(\x05\x12\x36\n\nassignment\x18\n \x01(\x0b\x32\".carbon.rtc.InterventionAssignment\"|\n\x11InterventionCause\x12\"\n\x1eINTERVENTION_CAUSE_UNSPECIFIED\x10\x00\x12\x14\n\x10SENSOR_TRIGGERED\x10\x01\x12\x18\n\x14SAFETY_DRIVER_ACTION\x10\x02\x12\x13\n\x0fTRACTOR_REQUEST\x10\x03\"B\n\x10InterventionList\x12.\n\x0cintervention\x18\x01 \x03(\x0b\x32\x18.carbon.rtc.Intervention\"Z\n\x16InterventionAssignment\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12/\n\x0b\x61ssigned_at\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"A\n\x18ListInterventionsRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\"`\n\x19ListInterventionsResponse\x12\x12\n\npage_token\x18\x01 \x01(\t\x12/\n\rinterventions\x18\x02 \x03(\x0b\x32\x18.carbon.rtc.Intervention\"K\n\x19\x43reateInterventionRequest\x12.\n\x0cintervention\x18\x01 \x01(\x0b\x32\x18.carbon.rtc.Intervention\"L\n\x1a\x43reateInterventionResponse\x12.\n\x0cintervention\x18\x01 \x01(\x0b\x32\x18.carbon.rtc.Intervention\"C\n\x14GetActiveTaskRequest\x12+\n\x10\x63urrent_location\x18\x02 \x01(\x0b\x32\x11.carbon.geo.Point\"7\n\x15GetActiveTaskResponse\x12\x1e\n\x04task\x18\x01 \x01(\x0b\x32\x10.carbon.rtc.Task\"!\n\x0eGetTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\"1\n\x0fGetTaskResponse\x12\x1e\n\x04task\x18\x01 \x01(\x0b\x32\x10.carbon.rtc.Task\"\xdd\x03\n\x11UpdateTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\x12%\n\x05state\x18\x02 \x01(\x0e\x32\x11.carbon.rtc.StateH\x00\x88\x01\x01\x12\x33\n\nstarted_at\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x01\x88\x01\x01\x12.\n\x0estart_location\x18\x04 \x01(\x0b\x32\x11.carbon.geo.PointH\x02\x88\x01\x01\x12\x1a\n\rstart_heading\x18\x05 \x01(\x01H\x03\x88\x01\x01\x12\x31\n\x08\x65nded_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x04\x88\x01\x01\x12,\n\x0c\x65nd_location\x18\x07 \x01(\x0b\x32\x11.carbon.geo.PointH\x05\x88\x01\x01\x12\x18\n\x0b\x65nd_heading\x18\x08 \x01(\x01H\x06\x88\x01\x01\x12\x18\n\x0bstatus_info\x18\t \x01(\tH\x07\x88\x01\x01\x42\x08\n\x06_stateB\r\n\x0b_started_atB\x11\n\x0f_start_locationB\x10\n\x0e_start_headingB\x0b\n\t_ended_atB\x0f\n\r_end_locationB\x0e\n\x0c_end_headingB\x0e\n\x0c_status_info\"4\n\x12UpdateTaskResponse\x12\x1e\n\x04task\x18\x01 \x01(\x0b\x32\x10.carbon.rtc.Task\"Z\n\x16StartManualTaskRequest\x12)\n\x0estart_location\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x15\n\rstart_heading\x18\x02 \x01(\x01\"U\n\x15StopManualTaskRequest\x12\'\n\x0c\x65nd_location\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x13\n\x0b\x65nd_heading\x18\x02 \x01(\x01*\x98\x01\n\x05State\x12\x15\n\x11STATE_UNSPECIFIED\x10\x00\x12\x0b\n\x07PENDING\x10\x01\x12\t\n\x05READY\x10\x02\x12\x0f\n\x0bIN_PROGRESS\x10\x03\x12\r\n\tCOMPLETED\x10\x04\x12\r\n\tCANCELLED\x10\x05\x12\n\n\x06PAUSED\x10\x06\x12\n\n\x06\x46\x41ILED\x10\x07\x12\x10\n\x0c\x41\x43KNOWLEDGED\x10\x08\x12\x07\n\x03NEW\x10\t2\xc9\x03\n\nJobService\x12\x63\n\x12\x43reateIntervention\x12%.carbon.rtc.CreateInterventionRequest\x1a&.carbon.rtc.CreateInterventionResponse\x12T\n\rGetActiveTask\x12 .carbon.rtc.GetActiveTaskRequest\x1a!.carbon.rtc.GetActiveTaskResponse\x12\x42\n\x07GetTask\x12\x1a.carbon.rtc.GetTaskRequest\x1a\x1b.carbon.rtc.GetTaskResponse\x12o\n\x16GetNextActiveObjective\x12).carbon.rtc.GetNextActiveObjectiveRequest\x1a*.carbon.rtc.GetNextActiveObjectiveResponse\x12K\n\nUpdateTask\x12\x1d.carbon.rtc.UpdateTaskRequest\x1a\x1e.carbon.rtc.UpdateTaskResponseB=Z;github.com/carbonrobotics/protos/golang/generated/proto/rtcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rtc.jobs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z;github.com/carbonrobotics/protos/golang/generated/proto/rtc'
  _globals['_STATE']._serialized_start=6386
  _globals['_STATE']._serialized_end=6538
  _globals['_OBJECTIVE']._serialized_start=170
  _globals['_OBJECTIVE']._serialized_end=500
  _globals['_OBJECTIVE_OBJECTIVETYPE']._serialized_start=433
  _globals['_OBJECTIVE_OBJECTIVETYPE']._serialized_end=500
  _globals['_LASERWEEDROWOBJECTIVEDATA']._serialized_start=502
  _globals['_LASERWEEDROWOBJECTIVEDATA']._serialized_end=583
  _globals['_OBJECTIVELIST']._serialized_start=585
  _globals['_OBJECTIVELIST']._serialized_end=643
  _globals['_LISTOBJECTIVESRESPONSE']._serialized_start=645
  _globals['_LISTOBJECTIVESRESPONSE']._serialized_end=732
  _globals['_GETNEXTACTIVEOBJECTIVEREQUEST']._serialized_start=734
  _globals['_GETNEXTACTIVEOBJECTIVEREQUEST']._serialized_end=787
  _globals['_GETNEXTACTIVEOBJECTIVERESPONSE']._serialized_start=789
  _globals['_GETNEXTACTIVEOBJECTIVERESPONSE']._serialized_end=863
  _globals['_OBJECTIVEASSIGNMENT']._serialized_start=865
  _globals['_OBJECTIVEASSIGNMENT']._serialized_end=942
  _globals['_OBJECTIVEASSIGNMENTLIST']._serialized_start=944
  _globals['_OBJECTIVEASSIGNMENTLIST']._serialized_end=1023
  _globals['_LISTOBJECTIVEASSIGNMENTSRESPONSE']._serialized_start=1025
  _globals['_LISTOBJECTIVEASSIGNMENTSRESPONSE']._serialized_end=1133
  _globals['_ROBOTWHITELISTENTRY']._serialized_start=1135
  _globals['_ROBOTWHITELISTENTRY']._serialized_end=1178
  _globals['_ROBOTWHITELIST']._serialized_start=1180
  _globals['_ROBOTWHITELIST']._serialized_end=1246
  _globals['_TASK']._serialized_start=1249
  _globals['_TASK']._serialized_end=2178
  _globals['_STOPAUTONOMYTASK']._serialized_start=2180
  _globals['_STOPAUTONOMYTASK']._serialized_end=2198
  _globals['_LASERWEEDTASK']._serialized_start=2201
  _globals['_LASERWEEDTASK']._serialized_end=2387
  _globals['_SEQUENCETASK']._serialized_start=2389
  _globals['_SEQUENCETASK']._serialized_end=2452
  _globals['_MANUALTASK']._serialized_start=2454
  _globals['_MANUALTASK']._serialized_end=2488
  _globals['_GOTOANDFACETASK']._serialized_start=2490
  _globals['_GOTOANDFACETASK']._serialized_end=2558
  _globals['_GOTOREVERSIBLEPATHTASK']._serialized_start=2560
  _globals['_GOTOREVERSIBLEPATHTASK']._serialized_end=2676
  _globals['_FOLLOWPATHTASK']._serialized_start=2678
  _globals['_FOLLOWPATHTASK']._serialized_end=2801
  _globals['_SPEEDSETTING']._serialized_start=2804
  _globals['_SPEEDSETTING']._serialized_end=2969
  _globals['_SETTRACTORSTATETASK']._serialized_start=2971
  _globals['_SETTRACTORSTATETASK']._serialized_end=3033
  _globals['_TRACTORSTATE']._serialized_start=3036
  _globals['_TRACTORSTATE']._serialized_end=3241
  _globals['_TRACTORSTATE_GEAR']._serialized_start=3140
  _globals['_TRACTORSTATE_GEAR']._serialized_end=3232
  _globals['_HITCHSTATE']._serialized_start=3244
  _globals['_HITCHSTATE']._serialized_end=3413
  _globals['_HITCHSTATE_HITCHCOMMAND']._serialized_start=3334
  _globals['_HITCHSTATE_HITCHCOMMAND']._serialized_end=3404
  _globals['_TASKLIST']._serialized_start=3415
  _globals['_TASKLIST']._serialized_end=3458
  _globals['_LISTTASKSRESPONSE']._serialized_start=3460
  _globals['_LISTTASKSRESPONSE']._serialized_end=3532
  _globals['_SPATIALPATHTOLERANCE']._serialized_start=3534
  _globals['_SPATIALPATHTOLERANCE']._serialized_end=3642
  _globals['_JOB']._serialized_start=3645
  _globals['_JOB']._serialized_end=4112
  _globals['_JOB_JOBTYPE']._serialized_start=4043
  _globals['_JOB_JOBTYPE']._serialized_end=4094
  _globals['_JOBLIST']._serialized_start=4114
  _globals['_JOBLIST']._serialized_end=4154
  _globals['_LISTJOBSRESPONSE']._serialized_start=4156
  _globals['_LISTJOBSRESPONSE']._serialized_end=4225
  _globals['_WORKORDER']._serialized_start=4228
  _globals['_WORKORDER']._serialized_end=4406
  _globals['_WORKORDERLIST']._serialized_start=4408
  _globals['_WORKORDERLIST']._serialized_end=4467
  _globals['_LISTWORKORDERSRESPONSE']._serialized_start=4469
  _globals['_LISTWORKORDERSRESPONSE']._serialized_end=4557
  _globals['_INTERVENTION']._serialized_start=4560
  _globals['_INTERVENTION']._serialized_end=4978
  _globals['_INTERVENTION_INTERVENTIONCAUSE']._serialized_start=4854
  _globals['_INTERVENTION_INTERVENTIONCAUSE']._serialized_end=4978
  _globals['_INTERVENTIONLIST']._serialized_start=4980
  _globals['_INTERVENTIONLIST']._serialized_end=5046
  _globals['_INTERVENTIONASSIGNMENT']._serialized_start=5048
  _globals['_INTERVENTIONASSIGNMENT']._serialized_end=5138
  _globals['_LISTINTERVENTIONSREQUEST']._serialized_start=5140
  _globals['_LISTINTERVENTIONSREQUEST']._serialized_end=5205
  _globals['_LISTINTERVENTIONSRESPONSE']._serialized_start=5207
  _globals['_LISTINTERVENTIONSRESPONSE']._serialized_end=5303
  _globals['_CREATEINTERVENTIONREQUEST']._serialized_start=5305
  _globals['_CREATEINTERVENTIONREQUEST']._serialized_end=5380
  _globals['_CREATEINTERVENTIONRESPONSE']._serialized_start=5382
  _globals['_CREATEINTERVENTIONRESPONSE']._serialized_end=5458
  _globals['_GETACTIVETASKREQUEST']._serialized_start=5460
  _globals['_GETACTIVETASKREQUEST']._serialized_end=5527
  _globals['_GETACTIVETASKRESPONSE']._serialized_start=5529
  _globals['_GETACTIVETASKRESPONSE']._serialized_end=5584
  _globals['_GETTASKREQUEST']._serialized_start=5586
  _globals['_GETTASKREQUEST']._serialized_end=5619
  _globals['_GETTASKRESPONSE']._serialized_start=5621
  _globals['_GETTASKRESPONSE']._serialized_end=5670
  _globals['_UPDATETASKREQUEST']._serialized_start=5673
  _globals['_UPDATETASKREQUEST']._serialized_end=6150
  _globals['_UPDATETASKRESPONSE']._serialized_start=6152
  _globals['_UPDATETASKRESPONSE']._serialized_end=6204
  _globals['_STARTMANUALTASKREQUEST']._serialized_start=6206
  _globals['_STARTMANUALTASKREQUEST']._serialized_end=6296
  _globals['_STOPMANUALTASKREQUEST']._serialized_start=6298
  _globals['_STOPMANUALTASKREQUEST']._serialized_end=6383
  _globals['_JOBSERVICE']._serialized_start=6541
  _globals['_JOBSERVICE']._serialized_end=6998
# @@protoc_insertion_point(module_scope)
