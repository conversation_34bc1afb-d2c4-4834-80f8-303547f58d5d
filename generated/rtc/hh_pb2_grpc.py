# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from generated.rtc import hh_pb2 as rtc_dot_hh__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in rtc/hh_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class RobotStateStub(object):
    """RobotState service is for informing the robot about cloud side state
    requirements.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SetRobotRequiredState = channel.unary_unary(
                '/carbon.rtc.RobotState/SetRobotRequiredState',
                request_serializer=rtc_dot_hh__pb2.SetRobotRequiredStateRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.GetNextRequiredState = channel.unary_unary(
                '/carbon.rtc.RobotState/GetNextRequiredState',
                request_serializer=rtc_dot_hh__pb2.GetRobotRequiredStateRequest.SerializeToString,
                response_deserializer=rtc_dot_hh__pb2.GetRobotRequiredStateResponse.FromString,
                _registered_method=True)
        self.RobotRequirementStream = channel.stream_stream(
                '/carbon.rtc.RobotState/RobotRequirementStream',
                request_serializer=rtc_dot_hh__pb2.RobotStatusInfo.SerializeToString,
                response_deserializer=rtc_dot_hh__pb2.RobotRequiredState.FromString,
                _registered_method=True)


class RobotStateServicer(object):
    """RobotState service is for informing the robot about cloud side state
    requirements.
    """

    def SetRobotRequiredState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextRequiredState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RobotRequirementStream(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RobotStateServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SetRobotRequiredState': grpc.unary_unary_rpc_method_handler(
                    servicer.SetRobotRequiredState,
                    request_deserializer=rtc_dot_hh__pb2.SetRobotRequiredStateRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'GetNextRequiredState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextRequiredState,
                    request_deserializer=rtc_dot_hh__pb2.GetRobotRequiredStateRequest.FromString,
                    response_serializer=rtc_dot_hh__pb2.GetRobotRequiredStateResponse.SerializeToString,
            ),
            'RobotRequirementStream': grpc.stream_stream_rpc_method_handler(
                    servicer.RobotRequirementStream,
                    request_deserializer=rtc_dot_hh__pb2.RobotStatusInfo.FromString,
                    response_serializer=rtc_dot_hh__pb2.RobotRequiredState.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.RobotState', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.rtc.RobotState', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class RobotState(object):
    """RobotState service is for informing the robot about cloud side state
    requirements.
    """

    @staticmethod
    def SetRobotRequiredState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.RobotState/SetRobotRequiredState',
            rtc_dot_hh__pb2.SetRobotRequiredStateRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextRequiredState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.RobotState/GetNextRequiredState',
            rtc_dot_hh__pb2.GetRobotRequiredStateRequest.SerializeToString,
            rtc_dot_hh__pb2.GetRobotRequiredStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RobotRequirementStream(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(
            request_iterator,
            target,
            '/carbon.rtc.RobotState/RobotRequirementStream',
            rtc_dot_hh__pb2.RobotStatusInfo.SerializeToString,
            rtc_dot_hh__pb2.RobotRequiredState.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
