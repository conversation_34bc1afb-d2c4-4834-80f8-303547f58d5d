# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: rtc/web_socket.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'rtc/web_socket.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.rtc import location_history_pb2 as rtc_dot_location__history__pb2
from generated.rtc import device_location_history_pb2 as rtc_dot_device__location__history__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14rtc/web_socket.proto\x12\ncarbon.rtc\x1a\x1artc/location_history.proto\x1a!rtc/device_location_history.proto\"\xb3\x01\n\x13SubscriptionRequest\x12\x32\n\x04type\x18\x01 \x01(\x0e\x32$.carbon.rtc.SubscriptionRequest.Type\x12*\n\x06\x64\x65vice\x18\x02 \x01(\x0b\x32\x1a.carbon.rtc.DeviceSelector\"<\n\x04Type\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\r\n\tSUBSCRIBE\x10\x01\x12\x0f\n\x0bUNSUBSCRIBE\x10\x02\"w\n\x0e\x44\x65viceSelector\x12\x30\n\x04type\x18\x01 \x01(\x0e\x32\".carbon.rtc.SubscriptionDeviceType\x12\x10\n\x06serial\x18\x02 \x01(\tH\x00\x12\x13\n\tdevice_id\x18\x03 \x01(\tH\x00\x42\x0c\n\nidentifier\"\xaf\x01\n\x13SubscriptionMessage\x12\x32\n\x04type\x18\x01 \x01(\x0e\x32$.carbon.rtc.SubscriptionMessage.Type\x12.\n\x08location\x18\x02 \x01(\x0b\x32\x1a.carbon.rtc.LocationUpdateH\x00\"*\n\x04Type\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08LOCATION\x10\x01\x42\x08\n\x06Update\"\xbd\x01\n\x0eLocationUpdate\x12*\n\x06\x64\x65vice\x18\x01 \x01(\x0b\x32\x1a.carbon.rtc.DeviceSelector\x12\x36\n\trobot_rec\x18\x03 \x01(\x0b\x32!.carbon.rtc.LocationHistoryRecordH\x00\x12=\n\ndevice_rec\x18\x04 \x01(\x0b\x32\'.carbon.rtc.DeviceLocationHistoryRecordH\x00\x42\x08\n\x06record\"?\n\x1cWebSocketAuthenticateRequest\x12\r\n\x05token\x18\x01 \x01(\t\x12\x10\n\x08\x61udience\x18\x02 \x01(\t\"Q\n\x11WebSocketResponse\x12+\n\x06status\x18\x01 \x01(\x0e\x32\x1b.carbon.rtc.WebSocketStatus\x12\x0f\n\x07message\x18\x02 \x01(\t*Y\n\x16SubscriptionDeviceType\x12(\n$SUBSCRIPTION_DEVICE_TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05ROBOT\x10\x01\x12\n\n\x06\x44\x45VICE\x10\x02*G\n\x0fWebSocketStatus\x12!\n\x1dWEB_SOCKET_STATUS_UNSPECIFIED\x10\x00\x12\x06\n\x02OK\x10\x01\x12\t\n\x05\x45RROR\x10\x02\x42=Z;github.com/carbonrobotics/protos/golang/generated/proto/rtcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rtc.web_socket_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z;github.com/carbonrobotics/protos/golang/generated/proto/rtc'
  _globals['_SUBSCRIPTIONDEVICETYPE']._serialized_start=920
  _globals['_SUBSCRIPTIONDEVICETYPE']._serialized_end=1009
  _globals['_WEBSOCKETSTATUS']._serialized_start=1011
  _globals['_WEBSOCKETSTATUS']._serialized_end=1082
  _globals['_SUBSCRIPTIONREQUEST']._serialized_start=100
  _globals['_SUBSCRIPTIONREQUEST']._serialized_end=279
  _globals['_SUBSCRIPTIONREQUEST_TYPE']._serialized_start=219
  _globals['_SUBSCRIPTIONREQUEST_TYPE']._serialized_end=279
  _globals['_DEVICESELECTOR']._serialized_start=281
  _globals['_DEVICESELECTOR']._serialized_end=400
  _globals['_SUBSCRIPTIONMESSAGE']._serialized_start=403
  _globals['_SUBSCRIPTIONMESSAGE']._serialized_end=578
  _globals['_SUBSCRIPTIONMESSAGE_TYPE']._serialized_start=526
  _globals['_SUBSCRIPTIONMESSAGE_TYPE']._serialized_end=568
  _globals['_LOCATIONUPDATE']._serialized_start=581
  _globals['_LOCATIONUPDATE']._serialized_end=770
  _globals['_WEBSOCKETAUTHENTICATEREQUEST']._serialized_start=772
  _globals['_WEBSOCKETAUTHENTICATEREQUEST']._serialized_end=835
  _globals['_WEBSOCKETRESPONSE']._serialized_start=837
  _globals['_WEBSOCKETRESPONSE']._serialized_end=918
# @@protoc_insertion_point(module_scope)
