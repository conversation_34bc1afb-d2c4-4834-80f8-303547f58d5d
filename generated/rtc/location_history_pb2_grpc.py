# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from generated.rtc import location_history_pb2 as rtc_dot_location__history__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in rtc/location_history_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class LocationHistoryStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.LogLocationHistory = channel.unary_unary(
                '/carbon.rtc.LocationHistory/LogLocationHistory',
                request_serializer=rtc_dot_location__history__pb2.LogLocationHistoryRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ListRobots = channel.unary_unary(
                '/carbon.rtc.LocationHistory/ListRobots',
                request_serializer=rtc_dot_location__history__pb2.ListRobotsRequest.SerializeToString,
                response_deserializer=rtc_dot_location__history__pb2.ListRobotsResponse.FromString,
                _registered_method=True)
        self.ListLocationHistory = channel.unary_unary(
                '/carbon.rtc.LocationHistory/ListLocationHistory',
                request_serializer=rtc_dot_location__history__pb2.ListLocationHistoryRequest.SerializeToString,
                response_deserializer=rtc_dot_location__history__pb2.ListLocationHistoryResponse.FromString,
                _registered_method=True)
        self.StreamLocation = channel.stream_unary(
                '/carbon.rtc.LocationHistory/StreamLocation',
                request_serializer=rtc_dot_location__history__pb2.LocationHistoryRecord.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class LocationHistoryServicer(object):
    """Missing associated documentation comment in .proto file."""

    def LogLocationHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListRobots(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListLocationHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StreamLocation(self, request_iterator, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LocationHistoryServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'LogLocationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.LogLocationHistory,
                    request_deserializer=rtc_dot_location__history__pb2.LogLocationHistoryRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ListRobots': grpc.unary_unary_rpc_method_handler(
                    servicer.ListRobots,
                    request_deserializer=rtc_dot_location__history__pb2.ListRobotsRequest.FromString,
                    response_serializer=rtc_dot_location__history__pb2.ListRobotsResponse.SerializeToString,
            ),
            'ListLocationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.ListLocationHistory,
                    request_deserializer=rtc_dot_location__history__pb2.ListLocationHistoryRequest.FromString,
                    response_serializer=rtc_dot_location__history__pb2.ListLocationHistoryResponse.SerializeToString,
            ),
            'StreamLocation': grpc.stream_unary_rpc_method_handler(
                    servicer.StreamLocation,
                    request_deserializer=rtc_dot_location__history__pb2.LocationHistoryRecord.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.LocationHistory', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.rtc.LocationHistory', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LocationHistory(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def LogLocationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.LocationHistory/LogLocationHistory',
            rtc_dot_location__history__pb2.LogLocationHistoryRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListRobots(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.LocationHistory/ListRobots',
            rtc_dot_location__history__pb2.ListRobotsRequest.SerializeToString,
            rtc_dot_location__history__pb2.ListRobotsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListLocationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.LocationHistory/ListLocationHistory',
            rtc_dot_location__history__pb2.ListLocationHistoryRequest.SerializeToString,
            rtc_dot_location__history__pb2.ListLocationHistoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StreamLocation(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_unary(
            request_iterator,
            target,
            '/carbon.rtc.LocationHistory/StreamLocation',
            rtc_dot_location__history__pb2.LocationHistoryRecord.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
