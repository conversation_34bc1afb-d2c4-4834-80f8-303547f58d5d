# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from generated.rtc import jobs_pb2 as rtc_dot_jobs__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in rtc/jobs_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class JobServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateIntervention = channel.unary_unary(
                '/carbon.rtc.JobService/CreateIntervention',
                request_serializer=rtc_dot_jobs__pb2.CreateInterventionRequest.SerializeToString,
                response_deserializer=rtc_dot_jobs__pb2.CreateInterventionResponse.FromString,
                _registered_method=True)
        self.GetActiveTask = channel.unary_unary(
                '/carbon.rtc.JobService/GetActiveTask',
                request_serializer=rtc_dot_jobs__pb2.GetActiveTaskRequest.SerializeToString,
                response_deserializer=rtc_dot_jobs__pb2.GetActiveTaskResponse.FromString,
                _registered_method=True)
        self.GetTask = channel.unary_unary(
                '/carbon.rtc.JobService/GetTask',
                request_serializer=rtc_dot_jobs__pb2.GetTaskRequest.SerializeToString,
                response_deserializer=rtc_dot_jobs__pb2.GetTaskResponse.FromString,
                _registered_method=True)
        self.GetNextActiveObjective = channel.unary_unary(
                '/carbon.rtc.JobService/GetNextActiveObjective',
                request_serializer=rtc_dot_jobs__pb2.GetNextActiveObjectiveRequest.SerializeToString,
                response_deserializer=rtc_dot_jobs__pb2.GetNextActiveObjectiveResponse.FromString,
                _registered_method=True)
        self.UpdateTask = channel.unary_unary(
                '/carbon.rtc.JobService/UpdateTask',
                request_serializer=rtc_dot_jobs__pb2.UpdateTaskRequest.SerializeToString,
                response_deserializer=rtc_dot_jobs__pb2.UpdateTaskResponse.FromString,
                _registered_method=True)


class JobServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateIntervention(self, request, context):
        """Robot API
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActiveTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveObjective(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_JobServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateIntervention': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIntervention,
                    request_deserializer=rtc_dot_jobs__pb2.CreateInterventionRequest.FromString,
                    response_serializer=rtc_dot_jobs__pb2.CreateInterventionResponse.SerializeToString,
            ),
            'GetActiveTask': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActiveTask,
                    request_deserializer=rtc_dot_jobs__pb2.GetActiveTaskRequest.FromString,
                    response_serializer=rtc_dot_jobs__pb2.GetActiveTaskResponse.SerializeToString,
            ),
            'GetTask': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTask,
                    request_deserializer=rtc_dot_jobs__pb2.GetTaskRequest.FromString,
                    response_serializer=rtc_dot_jobs__pb2.GetTaskResponse.SerializeToString,
            ),
            'GetNextActiveObjective': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveObjective,
                    request_deserializer=rtc_dot_jobs__pb2.GetNextActiveObjectiveRequest.FromString,
                    response_serializer=rtc_dot_jobs__pb2.GetNextActiveObjectiveResponse.SerializeToString,
            ),
            'UpdateTask': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateTask,
                    request_deserializer=rtc_dot_jobs__pb2.UpdateTaskRequest.FromString,
                    response_serializer=rtc_dot_jobs__pb2.UpdateTaskResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.JobService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.rtc.JobService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class JobService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateIntervention(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.JobService/CreateIntervention',
            rtc_dot_jobs__pb2.CreateInterventionRequest.SerializeToString,
            rtc_dot_jobs__pb2.CreateInterventionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetActiveTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.JobService/GetActiveTask',
            rtc_dot_jobs__pb2.GetActiveTaskRequest.SerializeToString,
            rtc_dot_jobs__pb2.GetActiveTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.JobService/GetTask',
            rtc_dot_jobs__pb2.GetTaskRequest.SerializeToString,
            rtc_dot_jobs__pb2.GetTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNextActiveObjective(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.JobService/GetNextActiveObjective',
            rtc_dot_jobs__pb2.GetNextActiveObjectiveRequest.SerializeToString,
            rtc_dot_jobs__pb2.GetNextActiveObjectiveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.JobService/UpdateTask',
            rtc_dot_jobs__pb2.UpdateTaskRequest.SerializeToString,
            rtc_dot_jobs__pb2.UpdateTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
