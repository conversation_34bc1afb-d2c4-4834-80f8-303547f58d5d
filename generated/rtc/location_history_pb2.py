# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: rtc/location_history.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'rtc/location_history.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from generated.geo import geo_pb2 as geo_dot_geo__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1artc/location_history.proto\x12\ncarbon.rtc\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\rgeo/geo.proto\"B\n\tRobotData\x12\x0f\n\x07task_id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\x08\x12\x14\n\x0cobjective_id\x18\x03 \x01(\x04\"\xbf\x01\n\x15LocationHistoryRecord\x12 \n\x05point\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12\x1c\n\x0fheading_degrees\x18\x04 \x01(\x01H\x00\x88\x01\x01\x12-\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12#\n\x04\x64\x61ta\x18\x03 \x01(\x0b\x32\x15.carbon.rtc.RobotDataB\x12\n\x10_heading_degrees\"O\n\x19LocationHistoryRecordList\x12\x32\n\x07records\x18\x01 \x03(\x0b\x32!.carbon.rtc.LocationHistoryRecord\"S\n\x19LogLocationHistoryRequest\x12\x36\n\x07history\x18\x01 \x01(\x0b\x32%.carbon.rtc.LocationHistoryRecordList\"Q\n\x11ListRobotsRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x15\n\rrobot_serials\x18\x03 \x03(\t\"W\n\x12ListRobotsResponse\x12\x17\n\x0fnext_page_token\x18\x01 \x01(\t\x12(\n\x06robots\x18\x02 \x03(\x0b\x32\x18.carbon.rtc.RobotSummary\"T\n\x0cRobotSummary\x12\x0e\n\x06serial\x18\x01 \x01(\t\x12\x34\n\tlast_seen\x18\x02 \x01(\x0b\x32!.carbon.rtc.LocationHistoryRecord\"\xfb\x01\n\x1aListLocationHistoryRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x14\n\x0crobot_serial\x18\x03 \x01(\t\x12)\n\x05start\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x64\x65sc\x18\x06 \x01(\x08\x12\x17\n\x0finclude_closest\x18\x07 \x01(\x08\x12\x0f\n\x07task_id\x18\x08 \x01(\x04\x12\x14\n\x0cobjective_id\x18\t \x01(\x04\"n\n\x1bListLocationHistoryResponse\x12\x17\n\x0fnext_page_token\x18\x01 \x01(\t\x12\x36\n\x07history\x18\x02 \x01(\x0b\x32%.carbon.rtc.LocationHistoryRecordList2\xea\x02\n\x0fLocationHistory\x12S\n\x12LogLocationHistory\x12%.carbon.rtc.LogLocationHistoryRequest\x1a\x16.google.protobuf.Empty\x12K\n\nListRobots\x12\x1d.carbon.rtc.ListRobotsRequest\x1a\x1e.carbon.rtc.ListRobotsResponse\x12\x66\n\x13ListLocationHistory\x12&.carbon.rtc.ListLocationHistoryRequest\x1a\'.carbon.rtc.ListLocationHistoryResponse\x12M\n\x0eStreamLocation\x12!.carbon.rtc.LocationHistoryRecord\x1a\x16.google.protobuf.Empty(\x01\x42=Z;github.com/carbonrobotics/protos/golang/generated/proto/rtcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rtc.location_history_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z;github.com/carbonrobotics/protos/golang/generated/proto/rtc'
  _globals['_ROBOTDATA']._serialized_start=119
  _globals['_ROBOTDATA']._serialized_end=185
  _globals['_LOCATIONHISTORYRECORD']._serialized_start=188
  _globals['_LOCATIONHISTORYRECORD']._serialized_end=379
  _globals['_LOCATIONHISTORYRECORDLIST']._serialized_start=381
  _globals['_LOCATIONHISTORYRECORDLIST']._serialized_end=460
  _globals['_LOGLOCATIONHISTORYREQUEST']._serialized_start=462
  _globals['_LOGLOCATIONHISTORYREQUEST']._serialized_end=545
  _globals['_LISTROBOTSREQUEST']._serialized_start=547
  _globals['_LISTROBOTSREQUEST']._serialized_end=628
  _globals['_LISTROBOTSRESPONSE']._serialized_start=630
  _globals['_LISTROBOTSRESPONSE']._serialized_end=717
  _globals['_ROBOTSUMMARY']._serialized_start=719
  _globals['_ROBOTSUMMARY']._serialized_end=803
  _globals['_LISTLOCATIONHISTORYREQUEST']._serialized_start=806
  _globals['_LISTLOCATIONHISTORYREQUEST']._serialized_end=1057
  _globals['_LISTLOCATIONHISTORYRESPONSE']._serialized_start=1059
  _globals['_LISTLOCATIONHISTORYRESPONSE']._serialized_end=1169
  _globals['_LOCATIONHISTORY']._serialized_start=1172
  _globals['_LOCATIONHISTORY']._serialized_end=1534
# @@protoc_insertion_point(module_scope)
