# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: rtc/hh.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'rtc/hh.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0crtc/hh.proto\x12\ncarbon.rtc\x1a\x1bgoogle/protobuf/empty.proto\"+\n\x12RobotRequiredState\x12\x15\n\rstopped_state\x18\x01 \x01(\x08\";\n\x0fRobotStatusInfo\x12(\n\x05state\x18\x01 \x01(\x0e\x32\x19.carbon.rtc.HHStateStatus\"\x82\x01\n\x1cSetRobotRequiredStateRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x02 \x01(\t\x12\x36\n\x0erequired_state\x18\x03 \x01(\x0b\x32\x1e.carbon.rtc.RobotRequiredState\"J\n\x1cGetRobotRequiredStateRequest\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x02 \x01(\t\"\x9f\x01\n\x1dGetRobotRequiredStateResponse\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x36\n\x0erequired_state\x18\x02 \x01(\x0b\x32\x1e.carbon.rtc.RobotRequiredState\x12\x30\n\x0bstatus_info\x18\x03 \x01(\x0b\x32\x1b.carbon.rtc.RobotStatusInfo*o\n\rHHStateStatus\x12\x0e\n\nHH_UNKNOWN\x10\x00\x12\x0f\n\x0bHH_DISABLED\x10\x01\x12\x12\n\x0eHH_OPERATIONAL\x10\x02\x12\x0e\n\nHH_STOPPED\x10\x03\x12\x0b\n\x07HH_SAFE\x10\x04\x12\x0c\n\x08HH_ESTOP\x10\x05\x32\xaf\x02\n\nRobotState\x12Y\n\x15SetRobotRequiredState\x12(.carbon.rtc.SetRobotRequiredStateRequest\x1a\x16.google.protobuf.Empty\x12k\n\x14GetNextRequiredState\x12(.carbon.rtc.GetRobotRequiredStateRequest\x1a).carbon.rtc.GetRobotRequiredStateResponse\x12Y\n\x16RobotRequirementStream\x12\x1b.carbon.rtc.RobotStatusInfo\x1a\x1e.carbon.rtc.RobotRequiredState(\x01\x30\x01\x42=Z;github.com/carbonrobotics/protos/golang/generated/proto/rtcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rtc.hh_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z;github.com/carbonrobotics/protos/golang/generated/proto/rtc'
  _globals['_HHSTATESTATUS']._serialized_start=534
  _globals['_HHSTATESTATUS']._serialized_end=645
  _globals['_ROBOTREQUIREDSTATE']._serialized_start=57
  _globals['_ROBOTREQUIREDSTATE']._serialized_end=100
  _globals['_ROBOTSTATUSINFO']._serialized_start=102
  _globals['_ROBOTSTATUSINFO']._serialized_end=161
  _globals['_SETROBOTREQUIREDSTATEREQUEST']._serialized_start=164
  _globals['_SETROBOTREQUIREDSTATEREQUEST']._serialized_end=294
  _globals['_GETROBOTREQUIREDSTATEREQUEST']._serialized_start=296
  _globals['_GETROBOTREQUIREDSTATEREQUEST']._serialized_end=370
  _globals['_GETROBOTREQUIREDSTATERESPONSE']._serialized_start=373
  _globals['_GETROBOTREQUIREDSTATERESPONSE']._serialized_end=532
  _globals['_ROBOTSTATE']._serialized_start=648
  _globals['_ROBOTSTATE']._serialized_end=951
# @@protoc_insertion_point(module_scope)
