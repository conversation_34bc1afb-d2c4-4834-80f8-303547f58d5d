# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from generated.rtc import device_location_history_pb2 as rtc_dot_device__location__history__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in rtc/device_location_history_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class DeviceLocationStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.LogLocationHistory = channel.unary_unary(
                '/carbon.rtc.DeviceLocation/LogLocationHistory',
                request_serializer=rtc_dot_device__location__history__pb2.LogDeviceLocationHistoryRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.ListDevices = channel.unary_unary(
                '/carbon.rtc.DeviceLocation/ListDevices',
                request_serializer=rtc_dot_device__location__history__pb2.ListDevicesRequest.SerializeToString,
                response_deserializer=rtc_dot_device__location__history__pb2.ListDevicesResponse.FromString,
                _registered_method=True)
        self.ListLocationHistory = channel.unary_unary(
                '/carbon.rtc.DeviceLocation/ListLocationHistory',
                request_serializer=rtc_dot_device__location__history__pb2.ListDeviceLocationHistoryRequest.SerializeToString,
                response_deserializer=rtc_dot_device__location__history__pb2.ListDeviceLocationHistoryResponse.FromString,
                _registered_method=True)


class DeviceLocationServicer(object):
    """Missing associated documentation comment in .proto file."""

    def LogLocationHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDevices(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListLocationHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DeviceLocationServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'LogLocationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.LogLocationHistory,
                    request_deserializer=rtc_dot_device__location__history__pb2.LogDeviceLocationHistoryRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'ListDevices': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDevices,
                    request_deserializer=rtc_dot_device__location__history__pb2.ListDevicesRequest.FromString,
                    response_serializer=rtc_dot_device__location__history__pb2.ListDevicesResponse.SerializeToString,
            ),
            'ListLocationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.ListLocationHistory,
                    request_deserializer=rtc_dot_device__location__history__pb2.ListDeviceLocationHistoryRequest.FromString,
                    response_serializer=rtc_dot_device__location__history__pb2.ListDeviceLocationHistoryResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.rtc.DeviceLocation', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('carbon.rtc.DeviceLocation', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class DeviceLocation(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def LogLocationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.DeviceLocation/LogLocationHistory',
            rtc_dot_device__location__history__pb2.LogDeviceLocationHistoryRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListDevices(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.DeviceLocation/ListDevices',
            rtc_dot_device__location__history__pb2.ListDevicesRequest.SerializeToString,
            rtc_dot_device__location__history__pb2.ListDevicesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListLocationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/carbon.rtc.DeviceLocation/ListLocationHistory',
            rtc_dot_device__location__history__pb2.ListDeviceLocationHistoryRequest.SerializeToString,
            rtc_dot_device__location__history__pb2.ListDeviceLocationHistoryResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
