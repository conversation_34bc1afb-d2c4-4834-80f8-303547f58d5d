# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: rtc/device_location_history.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'rtc/device_location_history.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from generated.geo import geo_pb2 as geo_dot_geo__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!rtc/device_location_history.proto\x12\ncarbon.rtc\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\rgeo/geo.proto\"\xae\x01\n\x1b\x44\x65viceLocationHistoryRecord\x12 \n\x05point\x18\x01 \x01(\x0b\x32\x11.carbon.geo.Point\x12-\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tdevice_id\x18\x03 \x01(\t\x12+\n\x0b\x64\x65vice_type\x18\x04 \x01(\x0e\x32\x16.carbon.rtc.DeviceType\"[\n\x1f\x44\x65viceLocationHistoryRecordList\x12\x38\n\x07records\x18\x01 \x03(\x0b\x32\'.carbon.rtc.DeviceLocationHistoryRecord\"_\n\x1fLogDeviceLocationHistoryRequest\x12<\n\x07history\x18\x01 \x01(\x0b\x32+.carbon.rtc.DeviceLocationHistoryRecordList\"O\n\x12ListDevicesRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x12\n\ndevice_ids\x18\x03 \x03(\t\"Z\n\x13ListDevicesResponse\x12\x17\n\x0fnext_page_token\x18\x01 \x01(\t\x12*\n\x07\x64\x65vices\x18\x02 \x03(\x0b\x32\x19.carbon.rtc.DeviceSummary\"W\n\rDeviceSummary\x12\n\n\x02id\x18\x01 \x01(\t\x12:\n\tlast_seen\x18\x02 \x01(\x0b\x32\'.carbon.rtc.DeviceLocationHistoryRecord\"\xd7\x01\n ListDeviceLocationHistoryRequest\x12\x11\n\tpage_size\x18\x01 \x01(\x05\x12\x12\n\npage_token\x18\x02 \x01(\t\x12\x11\n\tdevice_id\x18\x03 \x01(\t\x12)\n\x05start\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x64\x65sc\x18\x06 \x01(\x08\x12\x17\n\x0finclude_closest\x18\x07 \x01(\x08\"z\n!ListDeviceLocationHistoryResponse\x12\x17\n\x0fnext_page_token\x18\x01 \x01(\t\x12<\n\x07history\x18\x02 \x01(\x0b\x32+.carbon.rtc.DeviceLocationHistoryRecordList*L\n\nDeviceType\x12\x17\n\x13\x44\x45VICE_TYPE_UNKNOWN\x10\x00\x12\x10\n\x0cIRIDIUM_EDGE\x10\x01\x12\x13\n\x0fPEPLINK_GENERIC\x10\x02\x32\xaf\x02\n\x0e\x44\x65viceLocation\x12Y\n\x12LogLocationHistory\x12+.carbon.rtc.LogDeviceLocationHistoryRequest\x1a\x16.google.protobuf.Empty\x12N\n\x0bListDevices\x12\x1e.carbon.rtc.ListDevicesRequest\x1a\x1f.carbon.rtc.ListDevicesResponse\x12r\n\x13ListLocationHistory\x12,.carbon.rtc.ListDeviceLocationHistoryRequest\x1a-.carbon.rtc.ListDeviceLocationHistoryResponseB=Z;github.com/carbonrobotics/protos/golang/generated/proto/rtcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'rtc.device_location_history_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z;github.com/carbonrobotics/protos/golang/generated/proto/rtc'
  _globals['_DEVICETYPE']._serialized_start=1097
  _globals['_DEVICETYPE']._serialized_end=1173
  _globals['_DEVICELOCATIONHISTORYRECORD']._serialized_start=127
  _globals['_DEVICELOCATIONHISTORYRECORD']._serialized_end=301
  _globals['_DEVICELOCATIONHISTORYRECORDLIST']._serialized_start=303
  _globals['_DEVICELOCATIONHISTORYRECORDLIST']._serialized_end=394
  _globals['_LOGDEVICELOCATIONHISTORYREQUEST']._serialized_start=396
  _globals['_LOGDEVICELOCATIONHISTORYREQUEST']._serialized_end=491
  _globals['_LISTDEVICESREQUEST']._serialized_start=493
  _globals['_LISTDEVICESREQUEST']._serialized_end=572
  _globals['_LISTDEVICESRESPONSE']._serialized_start=574
  _globals['_LISTDEVICESRESPONSE']._serialized_end=664
  _globals['_DEVICESUMMARY']._serialized_start=666
  _globals['_DEVICESUMMARY']._serialized_end=753
  _globals['_LISTDEVICELOCATIONHISTORYREQUEST']._serialized_start=756
  _globals['_LISTDEVICELOCATIONHISTORYREQUEST']._serialized_end=971
  _globals['_LISTDEVICELOCATIONHISTORYRESPONSE']._serialized_start=973
  _globals['_LISTDEVICELOCATIONHISTORYRESPONSE']._serialized_end=1095
  _globals['_DEVICELOCATION']._serialized_start=1176
  _globals['_DEVICELOCATION']._serialized_end=1479
# @@protoc_insertion_point(module_scope)
