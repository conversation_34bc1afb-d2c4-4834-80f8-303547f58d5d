name: deeplearning_nightly_standard_jobs_dc

on:
  schedule:
    - cron: '0 7 * * *'  # midnight in Seattle
  pull_request:
    branches:
      - master
    paths:
      - "deeplearning/test/standard_jobs_test.py"
      - ".github/workflows/deeplearning_nightly_standard_jobs_dc.yml"
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  GITHUB_TOKEN: ${{ secrets.CR_PAT }}

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  nightly_standard_jobs_dc:
    runs-on: [self-hosted, Linux, dc_gpu]
    timeout-minutes: 360 # 6 hours
    permissions:
      actions: write
      contents: write
      packages: write
    env:
      MAKA_BOT_MODE: develop
      DEEPLEARNING_TAG: ci-${{ github.run_id }}-nightly-standard-jobs
      GIT_BRANCH: ${{github.head_ref}}
      REPO_DIR: ${{github.workspace}}
      CARBON_HOST_BASE_DIR: ${{github.workspace}}/..
      AUTH0_TOKEN_CACHE_FILE: /data/ml_auth_token_standard_nightly.json
      AUTH0_DOMAIN: ${{ secrets.AUTH0_DOMAIN }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      S3_CACHE_PROXY_SERVICE_HOST: frankenmini01.dc.carbonrobotics.com 
      TARGET_LATEST_TAG: latest
      NUM_GPUS: 2
      DOCKER_NETWORK: bridge
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Get current time
        uses: 1466587594/get-current-time@v2
        id: current-time
        with:
          format: MM-DD-YYYY
          utcOffset: "-08:00"
      - name: Load .env file
        uses: xom9ikk/dotenv@v2
        with:
          path: ${{github.workspace}}/../../..
      - name: Set env
        run: |
          echo "F_TIME=${{ steps.current-time.outputs.formattedTime }}" >> $GITHUB_ENV
          echo "MAKA_DATA_DIR=${{env.DATA_DIR}}" >> $GITHUB_ENV
          echo "CUDA_VISIBLE_DEVICES=${{ env.CUDA_VISIBLE_DEVICES }}" >> $GITHUB_ENV
          echo "DEVICES_STR=${{ env.DEVICES_STR }}" >> $GITHUB_ENV
      - name: Set permissions
        run: |
          chown -R $USER:$USER ${{ github.workspace }}
      - name: Cleanup previous runs
        run: |
          echo "Cleaning up previous run"
          rm -rf ${{ github.workspace }}
          mkdir ${{ github.workspace }}
          find ${{ github.workspace }} | xargs ls -al
          docker kill $(docker ps -q) || true
          docker rm $(docker ps -a -q) || true
      - name: Checkout branch
        uses: actions/checkout@v4.3.0
        with:
          fetch-depth: 0
          submodules: recursive
          token: ${{ secrets.CR_PAT }}
      - name: Remove previous lightning_logs
        run: |
          chown -R $USER:$USER ${{ github.workspace }}
          if [ -d "${{ github.workspace }}/lightning_logs" ]; then rm -rf ${{ github.workspace }}/lightning_logs/*; fi
      - name: Setup Bot Context
        run: |
          python3 ${{github.workspace}}/bot/initialize.py ci --repo ${{github.workspace}}

      - name: Pull Gobot Container
        uses: nick-fields/retry@v2
        with:
          max_attempts: 3
          retry_on: error
          timeout_seconds: 120
          retry_wait_seconds: 33
          command: docker pull ghcr.io/carbonrobotics/robot/gobot:latest

      - name: Build Gobot Container if it changed
        run: |
          docker buildx build -t ghcr.io/carbonrobotics/robot/gobot:latest --cache-from ghcr.io/carbonrobotics/robot/gobot:latest -f services/containers/gobot/Dockerfile .

      - name: Set docker tag
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=$BRANCH_TAG-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
            if [ "${{github.base_ref}}" != "master" ]; then # pr -> release branch
              echo "TARGET_LATEST_TAG=${${{github.base_ref}}##*/}" >> $GITHUB_ENV  # (release/X.Y -> X.Y)
            fi
          else
            echo "BRANCH_TAG=latest" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=latest-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
          fi

      - name: Pull Latest Containers
        run: |
          ${{github.workspace}}/bot/bot pull common || true
          ${{github.workspace}}/bot/bot pull deeplearning || true

      - name: Pull Branch Containers
        run: |
          ${{github.workspace}}/bot/bot pull --release $BRANCH_TAG || true
          ${{github.workspace}}/bot/bot tag --source $BRANCH_TAG --tag latest || true
          ${{github.workspace}}/bot/bot tag --source $BRANCH_TAG --tag ${{github.workflow}}-$BRANCH_TAG || true
        
      - name: Build Deeplearning Container
        run: |
          ${{github.workspace}}/bot/bot build --tag $DEEPLEARNING_TAG -a -ba GITHUB_TOKEN=${GITHUB_TOKEN} --cache-from ${{env.TARGET_LATEST_TAG}} -srt ${{env.TARGET_LATEST_TAG}} --container-build-retries 3 -p deeplearning
          echo "MAKA_DOCKER_DEEPLEARNING_TAG=ghcr.io/carbonrobotics/robot/deeplearning:$DEEPLEARNING_TAG" >> $GITHUB_ENV
          echo "MAKA_DOCKER_CONTAINER_NAME=$DEEPLEARNING_TAG" >> $GITHUB_ENV

      - name: Standard Jobs
        run: |
          USER=carbon  ./deeplearning/bin/run_docker.sh python -m deeplearning.test.standard_jobs_test
      - name: Clean old data
        if: ${{ always() }}
        run: |
          USER=carbon ./deeplearning/bin/run_docker.sh rm -rf /data/temp_stream_for_datasets || true
          USER=carbon ./deeplearning/bin/run_docker.sh rm -rf /data/deeplearning/snapshots || true 
          USER=carbon ./deeplearning/bin/run_docker.sh rm -rf /data/deeplearning/datasets || true
          USER=carbon ./deeplearning/bin/run_docker.sh rm -rf /data/deeplearning/evaluations || true
          USER=carbon ./deeplearning/bin/run_docker.sh rm -rf /data/deeplearning/comparison_data || true
      - name: Send success slack notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ success() }}
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: Night Train (choo choo)
          SLACK_COLOR: good
          SLACK_TITLE: Standard jobs run succeeded
          SLACK_MESSAGE: ':celebrate::train:'
      - name: Send failure slack notification
        uses: rtCamp/action-slack-notify@v2
        if: ${{ failure() }}
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_USERNAME: Night Train (choo choo)
          SLACK_COLOR: '#FF0000'
          SLACK_TITLE: Standard jobs run failed
          SLACK_MESSAGE: ':sadpanda::train:'
      - name: Clean container
        if: ${{ always() }}
        run: |
          docker rm -f ci-${{ github.run_id }}-nightly-standard-jobs || true
          docker rmi $(docker images | grep ${MAKA_DOCKER_CONTAINER_NAME} | tr -s ' ' | cut -d ' ' -f 3) || true
          docker rmi $(docker images | grep ${BRANCH_TAG} | tr -s ' ' | cut -d ' ' -f 3) || true
          docker rmi $(docker images | grep ${{github.workflow}}-${BRANCH_TAG} | tr -s ' ' | cut -d ' ' -f 3) || true
          docker rmi $(docker images | grep ci-${{ github.run_id }}-nightly-standard-jobs | tr -s ' ' | cut -d ' ' -f 3) || true
          docker rmi ci-${{ github.run_id }}-nightly-standard-jobs || true
          docker image prune -f || true
          docker system prune -f || true
          
          rm ~/.docker/config.json || true
